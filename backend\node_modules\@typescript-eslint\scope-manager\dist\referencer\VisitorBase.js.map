{"version": 3, "file": "VisitorBase.js", "sourceRoot": "", "sources": ["../../src/referencer/VisitorBase.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,kEAA2E;AAO3E,SAAS,QAAQ,CAAC,GAAY;IAC5B,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,IAAI,IAAI,CAAC;AAChD,CAAC;AACD,SAAS,MAAM,CAAC,IAAa;IAC3B,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC;AACzD,CAAC;AAMD,MAAe,WAAW;IAGxB,YAAY,OAAuB;QAF1B,gDAA+B;QAC/B,iEAA4C;QAEnD,uBAAA,IAAI,iCAAqB,OAAO,CAAC,gBAAgB,IAAI,0BAAW,MAAA,CAAC;QACjE,uBAAA,IAAI,kDACF,OAAO,CAAC,iCAAiC,IAAI,KAAK,MAAA,CAAC;IACvD,CAAC;IAED;;;;OAIG;IACH,aAAa,CACX,IAA0B,EAC1B,aAA0B,EAAE;QAE5B,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,UAAU,EAAE,QAAQ,CAAa,CAAC,CAAC;QAC/D,MAAM,QAAQ,GAAG,uBAAA,IAAI,qCAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxE,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;YAC3B,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBACrB,SAAS;YACX,CAAC;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,GAA0B,CAAY,CAAC;YAC1D,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,SAAS;YACX,CAAC;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE,CAAC;oBAC7B,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;wBACrB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;oBACvB,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAsC;QAC1C,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAI,IAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACzB,IAAI,CAAC,uBAAA,IAAI,sDAAmC,EAAE,CAAC;gBAC7C,OAAO;YACT,CAAC;QACH,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;CACF;AAEQ,kCAAW"}