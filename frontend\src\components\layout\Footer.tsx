'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Linkedin, Twitter, Github, Mail, Phone, MapPin } from 'lucide-react';
import { COMPANY_INFO } from '@bytesnbinary/shared';
import { NAVIGATION_ITEMS, SOCIAL_LINKS } from '@/lib/constants';
import { fadeInUp, staggerContainer } from '@/lib/utils';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  const iconMap = {
    Linkedin,
    Twitter,
    Github
  };

  return (
    <footer className="bg-muted/50 border-t border-border">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          variants={staggerContainer}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {/* Company Info */}
          <motion.div variants={fadeInUp} className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">BnB</span>
              </div>
              <span className="text-xl font-bold text-foreground">
                BytesNBinary Innovations
              </span>
            </div>
            <p className="text-muted-foreground mb-6 max-w-md">
              {COMPANY_INFO.description}
            </p>
            
            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-muted-foreground">
                <Mail className="w-4 h-4" />
                <a href={`mailto:${COMPANY_INFO.email}`} className="hover:text-primary transition-colors">
                  {COMPANY_INFO.email}
                </a>
              </div>
              <div className="flex items-center space-x-3 text-muted-foreground">
                <Phone className="w-4 h-4" />
                <a href={`tel:${COMPANY_INFO.phone}`} className="hover:text-primary transition-colors">
                  {COMPANY_INFO.phone}
                </a>
              </div>
              <div className="flex items-start space-x-3 text-muted-foreground">
                <MapPin className="w-4 h-4 mt-1" />
                <div>
                  <p>{COMPANY_INFO.address.street}</p>
                  <p>{COMPANY_INFO.address.city}, {COMPANY_INFO.address.state} {COMPANY_INFO.address.zipCode}</p>
                  <p>{COMPANY_INFO.address.country}</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div variants={fadeInUp}>
            <h3 className="text-lg font-semibold text-foreground mb-4">Quick Links</h3>
            <ul className="space-y-3">
              {NAVIGATION_ITEMS.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="text-muted-foreground hover:text-primary transition-colors"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Services */}
          <motion.div variants={fadeInUp}>
            <h3 className="text-lg font-semibold text-foreground mb-4">Services</h3>
            <ul className="space-y-3 text-muted-foreground">
              <li><Link href="/services#web-development" className="hover:text-primary transition-colors">Web Development</Link></li>
              <li><Link href="/services#mobile-development" className="hover:text-primary transition-colors">Mobile Apps</Link></li>
              <li><Link href="/services#cloud-devops" className="hover:text-primary transition-colors">Cloud & DevOps</Link></li>
              <li><Link href="/services#security" className="hover:text-primary transition-colors">Security</Link></li>
              <li><Link href="/services#ui-ux-design" className="hover:text-primary transition-colors">UI/UX Design</Link></li>
              <li><Link href="/services#ai-solutions" className="hover:text-primary transition-colors">AI Solutions</Link></li>
            </ul>
          </motion.div>
        </motion.div>

        {/* Bottom Section */}
        <motion.div
          variants={fadeInUp}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="border-t border-border mt-12 pt-8 flex flex-col md:flex-row justify-between items-center"
        >
          <p className="text-muted-foreground text-sm mb-4 md:mb-0">
            © {currentYear} BytesNBinary Innovations Pvt Ltd. All rights reserved.
          </p>
          
          {/* Social Links */}
          <div className="flex space-x-4">
            {SOCIAL_LINKS.map((social) => {
              const IconComponent = iconMap[social.icon as keyof typeof iconMap];
              return (
                <motion.a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <IconComponent className="w-5 h-5" />
                  <span className="sr-only">{social.name}</span>
                </motion.a>
              );
            })}
          </div>
        </motion.div>
      </div>
    </footer>
  );
}
