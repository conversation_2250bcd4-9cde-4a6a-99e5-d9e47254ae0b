import type { Metada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "BytesNBinary Innovations - Digital Solutions & IT Services",
  description:
    "Transform your business with BytesNBinary Innovations. We offer web development, mobile apps, cloud services, AI solutions, and comprehensive digital transformation services.",
  keywords:
    "web development, mobile apps, cloud services, AI solutions, digital transformation, IT consulting",
  authors: [{ name: "BytesNBinary Innovations" }],
  openGraph: {
    title: "BytesNBinary Innovations - Digital Solutions & IT Services",
    description:
      "Transform your business with comprehensive digital solutions from BytesNBinary Innovations.",
    url: "https://bytesnbinary.com",
    siteName: "BytesNBinary Innovations",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "BytesNBinary Innovations - Digital Solutions & IT Services",
    description:
      "Transform your business with comprehensive digital solutions from BytesNBinary Innovations.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Header />
        <main>{children}</main>
        <Footer />
      </body>
    </html>
  );
}
