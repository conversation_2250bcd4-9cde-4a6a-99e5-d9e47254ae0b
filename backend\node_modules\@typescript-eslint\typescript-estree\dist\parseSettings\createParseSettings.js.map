{"version": 3, "file": "createParseSettings.js", "sourceRoot": "", "sources": ["../../src/parseSettings/createParseSettings.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA0B;AAC1B,+CAAiC;AAGjC,iFAA8E;AAC9E,qDAA8D;AAE9D,kDAA+C;AAC/C,mDAGyB;AACzB,mEAAgE;AAEhE,qDAAkD;AAClD,6DAA0D;AAC1D,6DAA0D;AAE1D,MAAM,GAAG,GAAG,IAAA,eAAK,EACf,8EAA8E,CAC/E,CAAC;AAEF,IAAI,oBAA0D,CAAC;AAC/D,IAAI,wBAAwB,GAAkC,IAAI,CAAC;AAEnE,gGAAgG;AAChG,+GAA+G;AAC/G,uDAAuD;AACvD,gEAAgE;AAChE,MAAM,gBAAgB,GAAG;IACvB,QAAQ,EAAE,EAAE,CAAC,gBAAgB,EAAE,QAAQ;IACvC,SAAS,EAAE,EAAE,CAAC,gBAAgB,EAAE,SAAS;IACzC,kBAAkB,EAAE,EAAE,CAAC,gBAAgB,EAAE,kBAAkB;IAC3D,gBAAgB,EAAE,EAAE,CAAC,gBAAgB,EAAE,gBAAgB;CAC/C,CAAC;AACX,+DAA+D;AAE/D,SAAgB,mBAAmB,CACjC,IAA4B,EAC5B,UAAoC,EAAE;IAEtC,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC7C,MAAM,SAAS,GAAG,IAAA,+BAAc,EAAC,OAAO,CAAC,CAAC;IAC1C,MAAM,eAAe,GACnB,OAAO,OAAO,CAAC,eAAe,KAAK,QAAQ;QACzC,CAAC,CAAC,OAAO,CAAC,eAAe;QACzB,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;IACpB,MAAM,cAAc,GAAG,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,CAAC;IAC9D,MAAM,gBAAgB,GAAG,CAAC,GAAwB,EAAE;QAClD,QAAQ,OAAO,CAAC,gBAAgB,EAAE,CAAC;YACjC,KAAK,KAAK;gBACR,OAAO,gBAAgB,CAAC,QAAQ,CAAC;YAEnC,KAAK,MAAM;gBACT,OAAO,gBAAgB,CAAC,SAAS,CAAC;YAEpC,KAAK,WAAW;gBACd,OAAO,gBAAgB,CAAC,gBAAgB,CAAC;YAE3C;gBACE,OAAO,gBAAgB,CAAC,QAAQ,CAAC;QACrC,CAAC;IACH,CAAC,CAAC,EAAE,CAAC;IAEL,MAAM,aAAa,GAAyB;QAC1C,eAAe,EAAE,OAAO,CAAC,eAAe,KAAK,IAAI;QACjD,IAAI;QACJ,YAAY;QACZ,OAAO,EAAE,OAAO,CAAC,OAAO,KAAK,IAAI;QACjC,QAAQ,EAAE,EAAE;QACZ,gCAAgC;QAC9B,6FAA6F;QAC7F,OAAO,CAAC,gCAAgC,KAAK,IAAI;QACnD,UAAU,EACR,OAAO,CAAC,UAAU,KAAK,IAAI;YACzB,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,mBAAmB,CAAC,CAAC;YAChC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;gBACjC,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC7B,CAAC,CAAC,IAAI,GAAG,EAAE;QACjB,2CAA2C,EAAE,KAAK;QAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB,KAAK,IAAI;QAC7D,2BAA2B,EACzB,OAAO,CAAC,8BAA8B;YACtC,CAAC,OAAO,CAAC,OAAO;gBACd,OAAO,CAAC,8BAA8B,KAAK,KAAK;gBAChD,OAAO,CAAC,GAAG,CAAC,uCAAuC,KAAK,MAAM,CAAC;YAC/D,CAAC,CAAC,CAAC,wBAAwB,KAAK,IAAA,2CAAoB,EAChD,OAAO,CAAC,8BAA8B,EACtC,gBAAgB,CACjB,CAAC;YACJ,CAAC,CAAC,SAAS;QACf,gDAAgD,EAC9C,OAAO,CAAC,gDAAgD,KAAK,IAAI;QACnE,mBAAmB,EACjB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC;YAC1C,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,GAAG,KAAK,QAAQ,CAAC;YAC/D,CAAC,CAAC,OAAO,CAAC,mBAAmB;YAC7B,CAAC,CAAC,EAAE;QACR,QAAQ,EAAE,IAAA,2BAAkB,EAC1B,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS;YACpE,CAAC,CAAC,OAAO,CAAC,QAAQ;YAClB,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,EAC5B,eAAe,CAChB;QACD,gBAAgB;QAChB,GAAG,EAAE,OAAO,CAAC,GAAG,KAAK,IAAI;QACzB,GAAG,EAAE,OAAO,CAAC,GAAG,KAAK,IAAI;QACzB,GAAG,EACD,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU;YACpC,CAAC,CAAC,OAAO,CAAC,QAAQ;YAClB,CAAC,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK;gBAC1B,CAAC,CAAC,GAAS,EAAE,GAAE,CAAC,CAAC,2DAA2D;gBAC5E,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,iCAAiC;QACtD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,KAAK,KAAK;QACpD,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI;QACnE,QAAQ,EAAE,EAAE;QACZ,KAAK,EAAE,OAAO,CAAC,KAAK,KAAK,IAAI;QAC7B,SAAS;QACT,kCAAkC,EAChC,OAAO,CAAC,kCAAkC;YAC1C,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM;QACjC,MAAM,EAAE,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI;QAC3C,kBAAkB,EAAE,CAAC,oBAAoB,KAAK,IAAI,6BAAa,CAC7D,SAAS;YACP,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI;gBAC3B,uDAAuC,CAC5C,CAAC;QACF,eAAe;KAChB,CAAC;IAEF,8EAA8E;IAC9E,IAAI,aAAa,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;QACtC,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,IAAI,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,CAAC;YACtD,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QACD,IACE,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;YACtC,6EAA6E;YAC7E,eAAK,CAAC,OAAO,CAAC,4BAA4B,CAAC,EAC3C,CAAC;YACD,mGAAmG;YACnG,UAAU,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QACD,eAAK,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACrC,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;QACpC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CACb,qPAAqP,CACtP,CAAC;QACJ,CAAC;QACD,GAAG,CACD,gFAAgF,CACjF,CAAC;IACJ,CAAC;IAED,sEAAsE;IACtE,IAAI,CAAC,aAAa,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,2BAA2B,EAAE,CAAC;QAC1E,aAAa,CAAC,QAAQ,GAAG,IAAA,uCAAkB,EAAC;YAC1C,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,OAAO,EAAE,IAAA,6CAAqB,EAAC,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC;YAC9D,uBAAuB,EAAE,OAAO,CAAC,uBAAuB;YACxD,SAAS,EAAE,aAAa,CAAC,SAAS;YAClC,eAAe,EAAE,eAAe;SACjC,CAAC,CAAC;IACL,CAAC;IAED,yFAAyF;IACzF,yEAAyE;IACzE,IACE,OAAO,CAAC,gBAAgB,IAAI,IAAI;QAChC,aAAa,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC;QACnC,aAAa,CAAC,QAAQ,IAAI,IAAI;QAC9B,aAAa,CAAC,2BAA2B,IAAI,IAAI,EACjD,CAAC;QACD,aAAa,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,SAAS,CAAC;IAC9D,CAAC;IAED,IAAA,uCAAkB,EAAC,aAAa,EAAE,cAAc,CAAC,CAAC;IAElD,OAAO,aAAa,CAAC;AACvB,CAAC;AAnJD,kDAmJC;AAED,SAAgB,uBAAuB;IACrC,oBAAoB,EAAE,KAAK,EAAE,CAAC;AAChC,CAAC;AAFD,0DAEC;AAED,SAAgB,2BAA2B;IACzC,wBAAwB,GAAG,IAAI,CAAC;AAClC,CAAC;AAFD,kEAEC;AAED;;GAEG;AACH,SAAS,iBAAiB,CAAC,IAAa;IACtC,OAAO,IAAA,2BAAY,EAAC,IAAI,CAAC;QACvB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACxB,CAAC,CAAC,OAAO,IAAI,KAAK,QAAQ;YACxB,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrB,CAAC;AAED;;;;;GAKG;AACH,SAAS,WAAW,CAAC,GAAa;IAChC,OAAO,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC;AAC1C,CAAC"}