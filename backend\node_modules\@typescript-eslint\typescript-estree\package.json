{"name": "@typescript-eslint/typescript-estree", "version": "6.21.0", "description": "A parser that converts TypeScript source code into an ESTree compatible form", "files": ["dist", "_ts4.3", "README.md", "LICENSE"], "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json", "./use-at-your-own-risk": {"types": "./dist/use-at-your-own-risk.d.ts", "default": "./dist/use-at-your-own-risk.js"}}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/typescript-estree"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "license": "BSD-2-<PERSON><PERSON>", "keywords": ["ast", "estree", "ecmascript", "javascript", "typescript", "parser", "syntax"], "scripts": {"build": "tsc -b tsconfig.build.json", "postbuild": "downlevel-dts dist _ts4.3/dist --to=4.3", "clean": "tsc -b tsconfig.build.json --clean", "postclean": "rimraf dist && rimraf _ts4.3 && rimraf coverage", "format": "prettier --write \"./**/*.{ts,mts,cts,tsx,js,mjs,cjs,jsx,json,md,css}\" --ignore-path ../../.prettierignore", "lint": "npx nx lint", "test": "jest --coverage --runInBand --verbose", "typecheck": "tsc -p tsconfig.json --noEmit"}, "dependencies": {"@typescript-eslint/types": "6.21.0", "@typescript-eslint/visitor-keys": "6.21.0", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "minimatch": "9.0.3", "semver": "^7.5.4", "ts-api-utils": "^1.0.1"}, "devDependencies": {"@babel/code-frame": "*", "@babel/parser": "*", "glob": "*", "jest": "29.7.0", "jest-specific-snapshot": "^8.0.0", "make-dir": "*", "prettier": "^3.0.3", "rimraf": "*", "tmp": "*", "typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "typesVersions": {"<4.7": {"*": ["_ts4.3/*"]}}}