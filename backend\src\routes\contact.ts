import { Router, Request, Response } from 'express';
import { contactFormSchema, ApiResponse, ContactFormData } from '@bytesnbinary/shared';
import { emailService } from '../services/emailService';
import { contactFormLimiter } from '../middleware/rateLimiter';
import { asyncHandler } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

const router = Router();

router.post('/contact', contactFormLimiter, asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  try {
    // Validate request body
    const validationResult = contactFormSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid form data',
        error: validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
      });
    }

    const formData: ContactFormData = validationResult.data;

    // Log the contact form submission
    logger.info('Contact form submission received', {
      name: formData.name,
      email: formData.email,
      company: formData.company,
      serviceInterest: formData.serviceInterest,
      ip: req.ip
    });

    // Send emails
    await emailService.sendContactFormEmail(formData);
    
    // Send auto-reply (don't wait for it to complete)
    emailService.sendAutoReply(formData.email, formData.name).catch(err => {
      logger.warn('Auto-reply email failed', { error: err.message, email: formData.email });
    });

    res.status(200).json({
      success: true,
      message: 'Thank you for your message! We will get back to you within 24 hours.',
      data: {
        submittedAt: new Date().toISOString(),
        name: formData.name
      }
    });

  } catch (error) {
    logger.error('Contact form submission failed', { error, body: req.body });
    
    res.status(500).json({
      success: false,
      message: 'Failed to send your message. Please try again later.',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
}));

// Health check endpoint
router.get('/health', asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const emailConnected = await emailService.verifyConnection();
  
  res.status(200).json({
    success: true,
    message: 'API is healthy',
    data: {
      timestamp: new Date().toISOString(),
      emailService: emailConnected ? 'connected' : 'disconnected',
      uptime: process.uptime()
    }
  });
}));

export default router;
