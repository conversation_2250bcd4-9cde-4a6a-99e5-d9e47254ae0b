{"version": 3, "file": "lib.d.ts", "sourceRoot": "", "sources": ["../src/lib.ts"], "names": [], "mappings": "AAKA,KAAK,GAAG,GACJ,KAAK,GACL,KAAK,GACL,QAAQ,GACR,KAAK,GACL,QAAQ,GACR,QAAQ,GACR,QAAQ,GACR,QAAQ,GACR,QAAQ,GACR,QAAQ,GACR,QAAQ,GACR,QAAQ,GACR,QAAQ,GACR,KAAK,GACL,cAAc,GACd,WAAW,GACX,yBAAyB,GACzB,oBAAoB,GACpB,YAAY,GACZ,aAAa,GACb,mBAAmB,GACnB,kBAAkB,GAClB,iBAAiB,GACjB,gBAAgB,GAChB,cAAc,GACd,gBAAgB,GAChB,eAAe,GACf,yBAAyB,GACzB,sBAAsB,GACtB,aAAa,GACb,eAAe,GACf,qBAAqB,GACrB,eAAe,GACf,aAAa,GACb,oBAAoB,GACpB,uBAAuB,GACvB,sBAAsB,GACtB,aAAa,GACb,gBAAgB,GAChB,eAAe,GACf,cAAc,GACd,eAAe,GACf,eAAe,GACf,eAAe,GACf,aAAa,GACb,eAAe,GACf,aAAa,GACb,gBAAgB,GAChB,qBAAqB,GACrB,eAAe,GACf,yBAAyB,GACzB,aAAa,GACb,eAAe,GACf,gBAAgB,GAChB,eAAe,GACf,gBAAgB,GAChB,aAAa,GACb,cAAc,GACd,cAAc,GACd,aAAa,GACb,eAAe,GACf,qBAAqB,GACrB,eAAe,GACf,eAAe,GACf,cAAc,GACd,mBAAmB,GACnB,cAAc,GACd,mBAAmB,GACnB,eAAe,GACf,sBAAsB,GACtB,aAAa,GACb,mBAAmB,GACnB,eAAe,GACf,eAAe,GACf,gBAAgB,GAChB,gBAAgB,GAChB,mBAAmB,GACnB,YAAY,GACZ,mBAAmB,GACnB,aAAa,GACb,aAAa,GACb,aAAa,GACb,aAAa,GACb,aAAa,GACb,aAAa,GACb,aAAa,GACb,aAAa,GACb,aAAa,GACb,KAAK,CAAC;AAEV,OAAO,EAAE,GAAG,EAAE,CAAC"}