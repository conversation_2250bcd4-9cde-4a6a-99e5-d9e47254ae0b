(()=>{var e={};e.id=974,e.ids=[974],e.modules={295:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return o},isInterceptionRouteAppPath:function(){return a}});let n=r(8520),i=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function o(e){let t,r,a;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,a]=e.split(r,2);break}if(!t||!r||!a)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":a="/"===t?"/"+a:t+"/"+a;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let o=t.split("/");if(o.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});a=o.slice(0,-2).concat(a).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:a}}},527:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=r(3832);function i(e,t){let r=[],i=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,r);return(e,n)=>{if("string"!=typeof e)return!1;let i=a(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},776:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(7307),i=r(8470),a=r(5603),o=r(2530);function s(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.default,{}),(0,n.jsx)(a.default,{}),(0,n.jsx)(o.default,{})]})}},841:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1599:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},1732:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},1808:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return p},normalizeMetadataPageToRoute:function(){return m},normalizeMetadataRoute:function(){return f}});let n=r(6258),i=function(e){return e&&e.__esModule?e:{default:e}}(r(965)),a=r(5011),o=r(2417),s=r(7554),l=r(8520),c=r(1732),u=r(6093);function d(e){let t=i.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,u.isGroupSegment)(e)||(0,u.isParallelRouteSegment)(e))&&(r=(0,s.djb2Hash)(t).toString(36).slice(0,6)),r}function p(e,t,r){let n=(0,l.normalizeAppPath)(e),s=(0,o.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),u=(0,a.interpolateDynamicPath)(n,t,s),{name:p,ext:f}=i.default.parse(r),m=d(i.default.posix.join(e,p)),h=m?`-${m}`:"";return(0,c.normalizePathSep)(i.default.join(u,`${p}${h}${f}`))}function f(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=d(e),!t.endsWith("/route")){let{dir:e,name:n,ext:a}=i.default.parse(t);t=i.default.posix.join(e,`${n}${r?`-${r}`:""}${a}`,"route")}return t}function m(e,t){let r=e.endsWith("/route"),n=r?e.slice(0,-6):e,i=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${i}`)+(r?"/route":"")}},2417:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return h},getNamedRouteRegex:function(){return m},getRouteRegex:function(){return d},parseParameter:function(){return l}});let n=r(8541),i=r(295),a=r(9155),o=r(4953),s=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(s);return t?c(t[2]):c(e)}function c(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function u(e,t,r){let n={},l=1,u=[];for(let d of(0,o.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),o=d.match(s);if(e&&o&&o[2]){let{key:t,optional:r,repeat:i}=c(o[2]);n[t]={pos:l++,repeat:i,optional:r},u.push("/"+(0,a.escapeStringRegexp)(e)+"([^/]+?)")}else if(o&&o[2]){let{key:e,repeat:t,optional:i}=c(o[2]);n[e]={pos:l++,repeat:t,optional:i},r&&o[1]&&u.push("/"+(0,a.escapeStringRegexp)(o[1]));let s=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&o[1]&&(s=s.substring(1)),u.push(s)}else u.push("/"+(0,a.escapeStringRegexp)(d));t&&o&&o[3]&&u.push((0,a.escapeStringRegexp)(o[3]))}return{parameterizedRoute:u.join(""),groups:n}}function d(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:a,groups:o}=u(e,r,n),s=a;return i||(s+="(?:/)?"),{re:RegExp("^"+s+"$"),groups:o}}function p(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:i,routeKeys:o,keyPrefix:s,backreferenceDuplicateKeys:l}=e,{key:u,optional:d,repeat:p}=c(i),f=u.replace(/\W/g,"");s&&(f=""+s+f);let m=!1;(0===f.length||f.length>30)&&(m=!0),isNaN(parseInt(f.slice(0,1)))||(m=!0),m&&(f=n());let h=f in o;s?o[f]=""+s+u:o[f]=u;let g=r?(0,a.escapeStringRegexp)(r):"";return t=h&&l?"\\k<"+f+">":p?"(?<"+f+">.+?)":"(?<"+f+">[^/]+?)",d?"(?:/"+g+t+")?":"/"+g+t}function f(e,t,r,l,c){let u,d=(u=0,()=>{let e="",t=++u;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),f={},m=[];for(let u of(0,o.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>u.startsWith(e)),o=u.match(s);if(e&&o&&o[2])m.push(p({getSafeRouteKey:d,interceptionMarker:o[1],segment:o[2],routeKeys:f,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:c}));else if(o&&o[2]){l&&o[1]&&m.push("/"+(0,a.escapeStringRegexp)(o[1]));let e=p({getSafeRouteKey:d,segment:o[2],routeKeys:f,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:c});l&&o[1]&&(e=e.substring(1)),m.push(e)}else m.push("/"+(0,a.escapeStringRegexp)(u));r&&o&&o[3]&&m.push((0,a.escapeStringRegexp)(o[3]))}return{namedParameterizedRoute:m.join(""),routeKeys:f}}function m(e,t){var r,n,i;let a=f(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(i=t.backreferenceDuplicateKeys)&&i),o=a.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(o+="(?:/)?"),{...d(e,t),namedRegex:"^"+o+"$",routeKeys:a.routeKeys}}function h(e,t){let{parameterizedRoute:r}=u(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=f(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},2530:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(413).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\bnb-website\\\\frontend\\\\src\\\\components\\\\sections\\\\ServicesOverview.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\frontend\\src\\components\\sections\\ServicesOverview.tsx","default")},2648:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return u},matchHas:function(){return c},parseDestination:function(){return d},prepareDestination:function(){return p}});let n=r(3832),i=r(9155),a=r(7485),o=r(295),s=r(8906);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let i={},a=r=>{let n,a=r.key;switch(r.type){case"header":a=a.toLowerCase(),n=e.headers[a];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,s.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return i[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(a)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===r.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!r.every(e=>a(e))||n.some(e=>a(e)))&&i}function u(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,a.parseUrl)(t),n=r.pathname;n&&(n=l(n));let o=r.href;o&&(o=l(o));let s=r.hostname;s&&(s=l(s));let c=r.hash;return c&&(c=l(c)),{...r,pathname:n,hostname:s,href:o,hash:c}}function p(e){let t,r,i=Object.assign({},e.query),a=d(e),{hostname:s,query:c}=a,p=a.pathname;a.hash&&(p=""+p+a.hash);let f=[],m=[];for(let e of((0,n.pathToRegexp)(p,m),m))f.push(e.name);if(s){let e=[];for(let t of((0,n.pathToRegexp)(s,e),e))f.push(t.name)}let h=(0,n.compile)(p,{validate:!1});for(let[r,i]of(s&&(t=(0,n.compile)(s,{validate:!1})),Object.entries(c)))Array.isArray(i)?c[r]=i.map(t=>u(l(t),e.params)):"string"==typeof i&&(c[r]=u(l(i),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>f.includes(e)))for(let t of g)t in c||(c[t]=e.params[t]);if((0,o.isInterceptionRouteAppPath)(p))for(let t of p.split("/")){let r=o.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,i]=(r=h(e.params)).split("#",2);t&&(a.hostname=t(e.params)),a.pathname=n,a.hash=(i?"#":"")+(i||""),delete a.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return a.query={...i,...a.query},{newUrl:r,destQuery:c,parsedDestination:a}}},2805:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>p,ZB:()=>u,Zp:()=>l,aR:()=>c});var n=r(8625),i=r(4996),a=r.n(i),o=r(9659),s=r(7284);let l=a().forwardRef(({className:e,children:t,hover:r=!0,...i},a)=>(0,n.jsx)(o.P.div,{ref:a,className:(0,s.cn)("rounded-xl border border-border bg-background p-6 shadow-sm",e),whileHover:r?{y:-5,scale:1.02}:void 0,transition:{type:"spring",stiffness:300,damping:20},...i,children:t}));l.displayName="Card";let c=a().forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 pb-4",e),...t}));c.displayName="CardHeader";let u=a().forwardRef(({className:e,...t},r)=>(0,n.jsx)("h3",{ref:r,className:(0,s.cn)("text-xl font-semibold leading-none tracking-tight",e),...t}));u.displayName="CardTitle";let d=a().forwardRef(({className:e,...t},r)=>(0,n.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let p=a().forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,s.cn)("pt-0",e),...t}));p.displayName="CardContent",a().forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center pt-4",e),...t})).displayName="CardFooter"},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3232:(e,t,r)=>{"use strict";r.d(t,{default:()=>y});var n=r(8625);r(4996);var i=r(3948),a=r.n(i),o=r(9659),s=r(3922);let l=(0,s.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var c=r(9234);let u=(0,s.A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]),d=(0,s.A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]),p=(0,s.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var f=r(6180),m=r(7387),h=r(2805),g=r(7284);let x=[{icon:l,title:"Mission-Driven",description:"Empowering businesses through innovative technology solutions that drive real results."},{icon:c.A,title:"Expert Team",description:"Skilled professionals with years of experience in cutting-edge technologies."},{icon:u,title:"Innovation First",description:"We stay ahead of technology trends to deliver future-ready solutions."},{icon:d,title:"Quality Assured",description:"Rigorous testing and quality assurance processes ensure excellence."}],v=["End-to-end digital transformation","Scalable and secure solutions","Agile development methodology","Post-launch support and maintenance","Transparent communication","Competitive pricing"];function y(){return(0,n.jsx)("section",{className:"py-20 lg:py-32 bg-muted/30",children:(0,n.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20 items-center",children:[(0,n.jsxs)(o.P.div,{variants:g.bK,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},children:[(0,n.jsxs)(o.P.h2,{variants:g.xs,className:"text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6",children:["About ",(0,n.jsx)("span",{className:"text-primary",children:"BytesNBinary"})]}),(0,n.jsx)(o.P.p,{variants:g.xs,className:"text-lg text-muted-foreground mb-8 leading-relaxed",children:"BytesNBinary Innovations is a forward-thinking IT startup dedicated to transforming businesses through comprehensive digital solutions. We combine technical expertise with creative innovation to deliver exceptional results."}),(0,n.jsx)(o.P.div,{variants:g.xs,className:"space-y-4 mb-8",children:v.map((e,t)=>(0,n.jsxs)(o.P.div,{variants:g.xs,custom:t,className:"flex items-center space-x-3",children:[(0,n.jsx)(p,{className:"w-5 h-5 text-secondary flex-shrink-0"}),(0,n.jsx)("span",{className:"text-foreground",children:e})]},e))}),(0,n.jsx)(o.P.div,{variants:g.xs,children:(0,n.jsx)(a(),{href:"/about",className:"group",children:(0,n.jsxs)(m.$,{size:"lg",children:["Learn More About Us",(0,n.jsx)(f.A,{className:"ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform"})]})})})]}),(0,n.jsx)(o.P.div,{variants:g.bK,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:x.map((e,t)=>(0,n.jsx)(o.P.div,{variants:g.gy,custom:t,children:(0,n.jsxs)(h.Zp,{className:"h-full text-center group hover:shadow-lg transition-all duration-300",children:[(0,n.jsxs)(h.aR,{children:[(0,n.jsx)(o.P.div,{className:"w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl flex items-center justify-center group-hover:from-primary/20 group-hover:to-secondary/20 transition-all duration-300",whileHover:{scale:1.1,rotate:5},children:(0,n.jsx)(e.icon,{className:"w-6 h-6 text-primary"})}),(0,n.jsx)(h.ZB,{className:"text-lg",children:e.title})]}),(0,n.jsx)(h.Wu,{children:(0,n.jsx)("p",{className:"text-muted-foreground text-sm leading-relaxed",children:e.description})})]})},e.title))})]}),(0,n.jsx)(o.P.div,{variants:g.tE,initial:"hidden",whileInView:"visible",viewport:{once:!0},className:"mt-20 text-center",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,n.jsx)("h3",{className:"text-2xl lg:text-3xl font-bold text-foreground mb-6",children:"Our Mission"}),(0,n.jsx)("p",{className:"text-lg lg:text-xl text-muted-foreground leading-relaxed",children:'"To empower businesses of all sizes with innovative technology solutions that drive growth, enhance efficiency, and create lasting value in an increasingly digital world."'})]})})]})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3832:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",a=r+1;a<e.length;){var o=e.charCodeAt(a);if(o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122||95===o){i+=e[a++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=a;continue}if("("===n){var s=1,l="",a=r+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--s){a++;break}}else if("("===e[a]&&(s++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);l+=e[a++]}if(s)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=a;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,a=void 0===n?"./":n,o="[^"+i(t.delimiter||"/#?")+"]+?",s=[],l=0,c=0,u="",d=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},p=function(e){var t=d(e);if(void 0!==t)return t;var n=r[c];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},f=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};c<r.length;){var m=d("CHAR"),h=d("NAME"),g=d("PATTERN");if(h||g){var x=m||"";-1===a.indexOf(x)&&(u+=x,x=""),u&&(s.push(u),u=""),s.push({name:h||l++,prefix:x,suffix:"",pattern:g||o,modifier:d("MODIFIER")||""});continue}var v=m||d("ESCAPED_CHAR");if(v){u+=v;continue}if(u&&(s.push(u),u=""),d("OPEN")){var x=f(),y=d("NAME")||"",b=d("PATTERN")||"",E=f();p("CLOSE"),s.push({name:y||(b?l++:""),pattern:y&&!b?o:b,prefix:x,suffix:E,modifier:d("MODIFIER")||""});continue}p("END")}return s}function r(e,t){void 0===t&&(t={});var r=a(t),n=t.encode,i=void 0===n?function(e){return e}:n,o=t.validate,s=void 0===o||o,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var a=e[n];if("string"==typeof a){r+=a;continue}var o=t?t[a.name]:void 0,c="?"===a.modifier||"*"===a.modifier,u="*"===a.modifier||"+"===a.modifier;if(Array.isArray(o)){if(!u)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===o.length){if(c)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var d=0;d<o.length;d++){var p=i(o[d],a);if(s&&!l[n].test(p))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+p+'"');r+=a.prefix+p+a.suffix}continue}if("string"==typeof o||"number"==typeof o){var p=i(String(o),a);if(s&&!l[n].test(p))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+p+'"');r+=a.prefix+p+a.suffix;continue}if(!c){var f=u?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+f)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,i=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var a=n[0],o=n.index,s=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?s[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):s[r.name]=i(n[e],r)}}(l);return{path:a,index:o,params:s}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function o(e,t,r){void 0===r&&(r={});for(var n=r.strict,o=void 0!==n&&n,s=r.start,l=r.end,c=r.encode,u=void 0===c?function(e){return e}:c,d="["+i(r.endsWith||"")+"]|$",p="["+i(r.delimiter||"/#?")+"]",f=void 0===s||s?"^":"",m=0;m<e.length;m++){var h=e[m];if("string"==typeof h)f+=i(u(h));else{var g=i(u(h.prefix)),x=i(u(h.suffix));if(h.pattern)if(t&&t.push(h),g||x)if("+"===h.modifier||"*"===h.modifier){var v="*"===h.modifier?"?":"";f+="(?:"+g+"((?:"+h.pattern+")(?:"+x+g+"(?:"+h.pattern+"))*)"+x+")"+v}else f+="(?:"+g+"("+h.pattern+")"+x+")"+h.modifier;else f+="("+h.pattern+")"+h.modifier;else f+="(?:"+g+x+")"+h.modifier}}if(void 0===l||l)o||(f+=p+"?"),f+=r.endsWith?"(?="+d+")":"$";else{var y=e[e.length-1],b="string"==typeof y?p.indexOf(y[y.length-1])>-1:void 0===y;o||(f+="(?:"+p+"(?="+d+"))?"),b||(f+="(?="+p+"|"+d+")")}return new RegExp(f,a(r))}function s(t,r,n){if(t instanceof RegExp){if(!r)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var l=0;l<i.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return s(e,r,n).source}).join("|")+")",a(n)):o(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(s(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=o,t.pathToRegexp=s})(),e.exports=t})()},3873:e=>{"use strict";e.exports=require("path")},4638:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(1808);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},4933:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return x},NormalizeError:function(){return h},PageNotFoundError:function(){return g},SP:function(){return p},ST:function(){return f},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return s},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return y}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function o(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=o();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let p="undefined"!=typeof performance,f=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class h extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class x extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function y(e){return JSON.stringify({message:e.message,stack:e.stack})}},5011:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return x},getUtils:function(){return g},interpolateDynamicPath:function(){return m},normalizeDynamicRouteParams:function(){return h},normalizeVercelUrl:function(){return f}});let n=r(9551),i=r(665),a=r(527),o=r(2417),s=r(9351),l=r(2648),c=r(4953),u=r(8520),d=r(8541),p=r(5958);function f(e,t,r){let i=(0,n.parse)(e.url,!0);for(let e of(delete i.search,Object.keys(i.query))){let n=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),a=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(n||a||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete i.query[e]}e.url=(0,n.format)(i)}function m(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let i,{optional:a,repeat:o}=r.groups[n],s=`[${o?"...":""}${n}]`;a&&(s=`[${s}]`);let l=t[n];i=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(s,i)}return e}function h(e,t,r,n){let i={};for(let a of Object.keys(t.groups)){let o=e[a];"string"==typeof o?o=(0,u.normalizeRscURL)(o):Array.isArray(o)&&(o=o.map(u.normalizeRscURL));let s=r[a],l=t.groups[a].optional;if((Array.isArray(s)?s.some(e=>Array.isArray(o)?o.some(t=>t.includes(e)):null==o?void 0:o.includes(e)):null==o?void 0:o.includes(s))||void 0===o&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!o||Array.isArray(o)&&1===o.length&&("index"===o[0]||o[0]===`[[...${a}]]`))&&(o=void 0,delete e[a]),o&&"string"==typeof o&&t.groups[a].repeat&&(o=o.split("/")),o&&(i[a]=o)}return{params:i,hasValidParams:!0}}function g({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:u,trailingSlash:d,caseSensitive:g}){let x,v,y;return u&&(x=(0,o.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),y=(v=(0,s.getRouteMatcher)(x))(e)),{handleRewrites:function(o,s){let p={},f=s.pathname,m=n=>{let c=(0,a.getPathMatch)(n.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!s.pathname)return!1;let m=c(s.pathname);if((n.has||n.missing)&&m){let e=(0,l.matchHas)(o,s.query,n.has,n.missing);e?Object.assign(m,e):m=!1}if(m){let{parsedDestination:a,destQuery:o}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:m,query:s.query});if(a.protocol)return!0;if(Object.assign(p,o,m),Object.assign(s.query,a.query),delete a.query,Object.assign(s,a),!(f=s.pathname))return!1;if(r&&(f=f.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(f,t.locales);f=e.pathname,s.query.nextInternalLocale=e.detectedLocale||m.nextInternalLocale}if(f===e)return!0;if(u&&v){let e=v(f);if(e)return s.query={...s.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])m(e);if(f!==e){let t=!1;for(let e of n.afterFiles||[])if(t=m(e))break;if(!t&&!(()=>{let t=(0,c.removeTrailingSlash)(f||"");return t===(0,c.removeTrailingSlash)(e)||(null==v?void 0:v(t))})()){for(let e of n.fallback||[])if(t=m(e))break}}return p},defaultRouteRegex:x,dynamicRouteMatcher:v,defaultRouteMatches:y,getParamsFromRouteMatches:function(e){if(!x)return null;let{groups:t,routeKeys:r}=x,n=(0,s.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,p.normalizeNextQueryParam)(e);r&&(n[r]=t,delete n[e])}let i={};for(let e of Object.keys(r)){let a=r[e];if(!a)continue;let o=t[a],s=n[e];if(!o.optional&&!s)return null;i[o.pos]=s}return i}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>x&&y?h(e,x,y,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>f(e,t,x),interpolateDynamicPath:(e,t)=>m(e,t,x)}}function x(e,t){return"string"==typeof e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[d.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},5591:(e,t,r)=>{Promise.resolve().then(r.bind(r,5603)),Promise.resolve().then(r.bind(r,8470)),Promise.resolve().then(r.bind(r,2530))},5594:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),r(4933);let n=r(1599);function i(e,t,r){void 0===r&&(r=!0);let i=new URL("http://n"),a=t?new URL(t,i):e.startsWith(".")?new URL("http://n"):i,{pathname:o,searchParams:s,search:l,hash:c,href:u,origin:d}=new URL(e,a);if(d!==i.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:o,query:r?(0,n.searchParamsToUrlQuery)(s):void 0,search:l,hash:c,href:u.slice(d.length)}}},5603:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(413).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\bnb-website\\\\frontend\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\frontend\\src\\components\\sections\\AboutSection.tsx","default")},5784:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},5855:(e,t,r)=>{Promise.resolve().then(r.bind(r,3232)),Promise.resolve().then(r.bind(r,6365)),Promise.resolve().then(r.bind(r,6871))},6168:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c});var n=r(5853),i=r(554),a=r(708),o=r.n(a),s=r(8067),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,776)),"C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\frontend\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4638))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,5347)),"C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,2192,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,2137,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,8358,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4638))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\frontend\\src\\app\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6180:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(3922).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},6258:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return s},STATIC_METADATA_IMAGES:function(){return o},getExtensionRegexString:function(){return l},isMetadataPage:function(){return d},isMetadataRoute:function(){return p},isMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return u}});let n=r(1732),i=r(8520),a=r(5784),o={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},s=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function c(e,t,r){let i=(r?"":"?")+"$",a=`\\d?${r?"":"(-\\w{6})?"}`,s=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${i}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${i}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${i}`),RegExp(`[\\\\/]${o.icon.filename}${a}${l(o.icon.extensions,t)}${i}`),RegExp(`[\\\\/]${o.apple.filename}${a}${l(o.apple.extensions,t)}${i}`),RegExp(`[\\\\/]${o.openGraph.filename}${a}${l(o.openGraph.extensions,t)}${i}`),RegExp(`[\\\\/]${o.twitter.filename}${a}${l(o.twitter.extensions,t)}${i}`)],c=(0,n.normalizePathSep)(e);return s.some(e=>e.test(c))}function u(e){let t=e.replace(/\/route$/,"");return(0,a.isAppRouteRoute)(e)&&c(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function d(e){return!(0,a.isAppRouteRoute)(e)&&c(e,[],!1)}function p(e){let t=(0,i.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,a.isAppRouteRoute)(e)&&c(t,[],!1)}},6365:(e,t,r)=>{"use strict";r.d(t,{default:()=>g});var n=r(8625);r(4996);var i=r(3948),a=r.n(i),o=r(9659),s=r(9841),l=r(3922);let c=(0,l.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);var u=r(9234),d=r(6180);let p=(0,l.A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);var f=r(7387),m=r(4236),h=r(7284);function g(){return(0,n.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-background to-muted/20",children:[(0,n.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,n.jsx)(o.P.div,{className:"absolute -top-40 -right-40 w-80 h-80 bg-primary/10 rounded-full blur-3xl",animate:{scale:[1,1.2,1],rotate:[0,180,360]},transition:{duration:20,repeat:1/0,ease:"linear"}}),(0,n.jsx)(o.P.div,{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-secondary/10 rounded-full blur-3xl",animate:{scale:[1.2,1,1.2],rotate:[360,180,0]},transition:{duration:25,repeat:1/0,ease:"linear"}})]}),(0,n.jsx)(o.P.div,{className:"absolute top-20 left-10 text-primary/20",animate:{y:[0,-20,0],rotate:[0,10,0]},transition:{duration:4,repeat:1/0,ease:"easeInOut"},children:(0,n.jsx)(s.A,{className:"w-8 h-8"})}),(0,n.jsx)(o.P.div,{className:"absolute top-40 right-20 text-secondary/20",animate:{y:[0,20,0],rotate:[0,-10,0]},transition:{duration:5,repeat:1/0,ease:"easeInOut"},children:(0,n.jsx)(c,{className:"w-6 h-6"})}),(0,n.jsx)(o.P.div,{className:"absolute bottom-40 left-20 text-accent/20",animate:{y:[0,-15,0],rotate:[0,15,0]},transition:{duration:6,repeat:1/0,ease:"easeInOut"},children:(0,n.jsx)(u.A,{className:"w-7 h-7"})}),(0,n.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:(0,n.jsxs)(o.P.div,{variants:h.bK,initial:"hidden",animate:"visible",className:"text-center max-w-5xl mx-auto",children:[(0,n.jsxs)(o.P.h1,{variants:h.tE,className:"text-4xl sm:text-5xl lg:text-7xl font-bold text-foreground mb-6 leading-tight",children:["Transforming Ideas into"," ",(0,n.jsx)("span",{className:"bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent",children:"Digital Solutions"})]}),(0,n.jsx)(o.P.p,{variants:h.tE,className:"text-lg sm:text-xl lg:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed",children:"BytesNBinary Innovations - Your Partner in Digital Excellence. Empowering businesses through innovative technology solutions."}),(0,n.jsxs)(o.P.div,{variants:h.tE,className:"flex flex-col sm:flex-row gap-4 justify-center items-center mb-16",children:[(0,n.jsx)(a(),{href:"/contact",className:"group",children:(0,n.jsxs)(f.$,{size:"lg",children:["Get Started",(0,n.jsx)(d.A,{className:"ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform"})]})}),(0,n.jsx)(a(),{href:"/services",className:"group",children:(0,n.jsxs)(f.$,{variant:"outline",size:"lg",children:[(0,n.jsx)(p,{className:"mr-2 w-4 h-4"}),"View Our Work"]})})]}),(0,n.jsx)(o.P.div,{variants:h.bK,className:"grid grid-cols-2 lg:grid-cols-4 gap-8 max-w-4xl mx-auto",children:m.aK.map((e,t)=>(0,n.jsxs)(o.P.div,{variants:t%2==0?h.xs:h.gy,className:"text-center",children:[(0,n.jsx)(o.P.div,{className:"text-3xl lg:text-4xl font-bold text-primary mb-2",initial:{scale:0},animate:{scale:1},transition:{delay:1+.2*t,type:"spring",stiffness:200,damping:10},children:e.value}),(0,n.jsx)("div",{className:"text-sm lg:text-base text-muted-foreground font-medium",children:e.label})]},e.label))})]})}),(0,n.jsx)(o.P.div,{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:2,duration:.5},children:(0,n.jsx)(o.P.div,{className:"w-6 h-10 border-2 border-muted-foreground rounded-full flex justify-center",animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},children:(0,n.jsx)(o.P.div,{className:"w-1 h-3 bg-muted-foreground rounded-full mt-2",animate:{scaleY:[1,.5,1]},transition:{duration:2,repeat:1/0}})})})]})}},6871:(e,t,r)=>{"use strict";r.d(t,{default:()=>j});var n=r(8625);r(4996);var i=r(3948),a=r.n(i),o=r(9659),s=r(9841),l=r(3922);let c=(0,l.A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),u=(0,l.A)("cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]),d=(0,l.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),p=(0,l.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),f=(0,l.A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),m=(0,l.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),h=(0,l.A)("layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]]);var g=r(6180),x=r(2805),v=r(7387),y=r(4236),b=r(7284);let E={Code:s.A,Smartphone:c,Cloud:u,Shield:d,Palette:p,Brain:f,TrendingUp:m,Layers:h};function j(){return(0,n.jsx)("section",{className:"py-20 lg:py-32 bg-background",children:(0,n.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,n.jsxs)(o.P.div,{variants:b.bK,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},className:"text-center mb-16",children:[(0,n.jsxs)(o.P.h2,{variants:b.tE,className:"text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6",children:["Our ",(0,n.jsx)("span",{className:"text-primary",children:"Services"})]}),(0,n.jsx)(o.P.p,{variants:b.tE,className:"text-lg lg:text-xl text-muted-foreground max-w-3xl mx-auto",children:"We offer comprehensive digital solutions to help your business thrive in the modern world. From web development to AI solutions, we've got you covered."})]}),(0,n.jsx)(o.P.div,{variants:b.bK,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-50px"},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12",children:y.Gp.map((e,t)=>{let r=E[e.icon];return(0,n.jsx)(o.P.div,{variants:b.Yo,custom:t,children:(0,n.jsxs)(x.Zp,{className:"h-full group cursor-pointer border-2 hover:border-primary/50 transition-all duration-300",children:[(0,n.jsxs)(x.aR,{className:"text-center",children:[(0,n.jsx)(o.P.div,{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl flex items-center justify-center group-hover:from-primary/20 group-hover:to-secondary/20 transition-all duration-300",whileHover:{scale:1.1,rotate:5},children:(0,n.jsx)(r,{className:"w-8 h-8 text-primary"})}),(0,n.jsx)(x.ZB,{className:"text-lg group-hover:text-primary transition-colors",children:e.title})]}),(0,n.jsxs)(x.Wu,{children:[(0,n.jsx)(x.BT,{className:"text-center line-clamp-3",children:e.description}),(0,n.jsxs)("div",{className:"mt-4 flex flex-wrap gap-2 justify-center",children:[e.features.slice(0,2).map(e=>(0,n.jsx)("span",{className:"text-xs px-2 py-1 bg-muted rounded-full text-muted-foreground",children:e},e)),e.features.length>2&&(0,n.jsxs)("span",{className:"text-xs px-2 py-1 bg-muted rounded-full text-muted-foreground",children:["+",e.features.length-2," more"]})]})]})]})},e.id)})}),(0,n.jsx)(o.P.div,{variants:b.tE,initial:"hidden",whileInView:"visible",viewport:{once:!0},className:"text-center",children:(0,n.jsx)(a(),{href:"/services",className:"group",children:(0,n.jsxs)(v.$,{size:"lg",children:["View All Services",(0,n.jsx)(g.A,{className:"ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform"})]})})})]})})}},7485:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let n=r(1599),i=r(5594);function a(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},7554:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},8353:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(n),o=(r||{}).decode||e,s=0;s<a.length;s++){var l=a[s],c=l.indexOf("=");if(!(c<0)){var u=l.substr(0,c).trim(),d=l.substr(++c,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[u]&&(i[u]=function(e,t){try{return t(e)}catch(t){return e}}(d,o))}}return i},t.serialize=function(e,t,n){var a=n||{},o=a.encode||r;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=o(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var l=e+"="+s;if(null!=a.maxAge){var c=a.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},8470:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(413).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\bnb-website\\\\frontend\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\frontend\\src\\components\\sections\\HeroSection.tsx","default")},8520:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return o}});let n=r(841),i=r(6093);function a(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function o(e){return e.replace(/\.rsc($|\?)/,"$1")}},8906:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(8353);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9155:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},9234:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(3922).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9351:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(4933);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},o={};for(let[e,t]of Object.entries(r)){let r=i[t.pos];void 0!==r&&(t.repeat?o[e]=r.split("/").map(e=>a(e)):o[e]=a(r))}return o}}},9551:e=>{"use strict";e.exports=require("url")},9841:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(3922).A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]])}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[683,382,19],()=>r(6168));module.exports=n})();