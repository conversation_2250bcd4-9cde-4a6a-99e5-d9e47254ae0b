import nodemailer from "nodemailer";
import { ContactFormData } from "@bytesnbinary/shared";
import { config } from "../utils/config";
import { logger } from "../utils/logger";

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport(config.email.smtp);
  }

  async sendContactFormEmail(data: ContactFormData): Promise<void> {
    try {
      const { name, email, phone, company, serviceInterest, message } = data;

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #1E40AF; border-bottom: 2px solid #1E40AF; padding-bottom: 10px;">
            New Contact Form Submission
          </h2>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #333; margin-top: 0;">Contact Information</h3>
            <p><strong>Name:</strong> ${name}</p>
            <p><strong>Email:</strong> <a href="mailto:${email}">${email}</a></p>
            ${phone ? `<p><strong>Phone:</strong> ${phone}</p>` : ""}
            ${company ? `<p><strong>Company:</strong> ${company}</p>` : ""}
            ${
              serviceInterest
                ? `<p><strong>Service Interest:</strong> ${serviceInterest}</p>`
                : ""
            }
          </div>
          
          <div style="background-color: #fff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px;">
            <h3 style="color: #333; margin-top: 0;">Message</h3>
            <p style="line-height: 1.6; white-space: pre-wrap;">${message}</p>
          </div>
          
          <div style="margin-top: 20px; padding: 15px; background-color: #e3f2fd; border-radius: 8px;">
            <p style="margin: 0; font-size: 14px; color: #666;">
              This email was sent from the BytesNBinary Innovations website contact form.
              <br>
              Submitted on: ${new Date().toLocaleString()}
            </p>
          </div>
        </div>
      `;

      const textContent = `
New Contact Form Submission

Contact Information:
Name: ${name}
Email: ${email}
${phone ? `Phone: ${phone}` : ""}
${company ? `Company: ${company}` : ""}
${serviceInterest ? `Service Interest: ${serviceInterest}` : ""}

Message:
${message}

Submitted on: ${new Date().toLocaleString()}
      `;

      await this.transporter.sendMail({
        from: config.email.from,
        to: config.email.to,
        subject: `New Contact Form Submission from ${name}`,
        text: textContent,
        html: htmlContent,
      });

      logger.info("Contact form email sent successfully", { email, name });
    } catch (error) {
      logger.error("Failed to send contact form email", { error, data });
      throw new Error("Failed to send email");
    }
  }

  async sendAutoReply(email: string, name: string): Promise<void> {
    try {
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #1E40AF; border-bottom: 2px solid #1E40AF; padding-bottom: 10px;">
            Thank you for contacting BytesNBinary Innovations!
          </h2>
          
          <p>Dear ${name},</p>
          
          <p>Thank you for reaching out to us. We have received your message and will get back to you within 24 hours.</p>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #333; margin-top: 0;">What happens next?</h3>
            <ul style="line-height: 1.6;">
              <li>Our team will review your inquiry</li>
              <li>We'll prepare a tailored response based on your needs</li>
              <li>You'll hear from us within 24 hours</li>
              <li>We'll schedule a consultation if needed</li>
            </ul>
          </div>
          
          <p>In the meantime, feel free to explore our services and learn more about how we can help transform your digital presence.</p>
          
          <div style="margin-top: 30px; padding: 20px; background-color: #e3f2fd; border-radius: 8px;">
            <h4 style="color: #1E40AF; margin-top: 0;">BytesNBinary Innovations</h4>
            <p style="margin: 5px 0;">Email: <EMAIL></p>
            <p style="margin: 5px 0;">Phone: +****************</p>
            <p style="margin: 5px 0;">Website: www.bytesnbinary.com</p>
          </div>
          
          <p style="font-size: 14px; color: #666; margin-top: 20px;">
            This is an automated response. Please do not reply to this email.
          </p>
        </div>
      `;

      await this.transporter.sendMail({
        from: config.email.from,
        to: email,
        subject: "Thank you for contacting BytesNBinary Innovations",
        html: htmlContent,
      });

      logger.info("Auto-reply email sent successfully", { email, name });
    } catch (error) {
      logger.error("Failed to send auto-reply email", { error, email, name });
      // Don't throw error for auto-reply failure
    }
  }

  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      logger.info("Email service connection verified");
      return true;
    } catch (error) {
      logger.error("Email service connection failed", { error });
      return false;
    }
  }
}

export const emailService = new EmailService();
