[{"name": "generate-buildid", "duration": 232, "timestamp": 1218379080561, "id": 4, "parentId": 1, "tags": {}, "startTime": 1752137270307, "traceId": "50c5f293b46e8fdf"}, {"name": "load-custom-routes", "duration": 303, "timestamp": 1218379080953, "id": 5, "parentId": 1, "tags": {}, "startTime": 1752137270307, "traceId": "50c5f293b46e8fdf"}, {"name": "create-dist-dir", "duration": 961, "timestamp": 1218379217657, "id": 6, "parentId": 1, "tags": {}, "startTime": 1752137270444, "traceId": "50c5f293b46e8fdf"}, {"name": "create-pages-mapping", "duration": 291, "timestamp": 1218379234280, "id": 7, "parentId": 1, "tags": {}, "startTime": 1752137270460, "traceId": "50c5f293b46e8fdf"}, {"name": "collect-app-paths", "duration": 5587, "timestamp": 1218379234624, "id": 8, "parentId": 1, "tags": {}, "startTime": 1752137270461, "traceId": "50c5f293b46e8fdf"}, {"name": "create-app-mapping", "duration": 3755, "timestamp": 1218379240399, "id": 9, "parentId": 1, "tags": {}, "startTime": 1752137270466, "traceId": "50c5f293b46e8fdf"}, {"name": "public-dir-conflict-check", "duration": 1929, "timestamp": 1218379244982, "id": 10, "parentId": 1, "tags": {}, "startTime": 1752137270471, "traceId": "50c5f293b46e8fdf"}, {"name": "generate-routes-manifest", "duration": 3344, "timestamp": 1218379247334, "id": 11, "parentId": 1, "tags": {}, "startTime": 1752137270473, "traceId": "50c5f293b46e8fdf"}, {"name": "create-entrypoints", "duration": 34055, "timestamp": 1218380381406, "id": 15, "parentId": 13, "tags": {}, "startTime": 1752137271608, "traceId": "50c5f293b46e8fdf"}, {"name": "generate-webpack-config", "duration": 477475, "timestamp": 1218380415718, "id": 16, "parentId": 14, "tags": {}, "startTime": 1752137271642, "traceId": "50c5f293b46e8fdf"}, {"name": "next-trace-entrypoint-plugin", "duration": 3612, "timestamp": 1218381096630, "id": 18, "parentId": 17, "tags": {}, "startTime": 1752137272323, "traceId": "50c5f293b46e8fdf"}, {"name": "add-entry", "duration": 337150, "timestamp": 1218381109551, "id": 22, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1752137272336, "traceId": "50c5f293b46e8fdf"}, {"name": "add-entry", "duration": 376484, "timestamp": 1218381109614, "id": 25, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1752137272336, "traceId": "50c5f293b46e8fdf"}, {"name": "add-entry", "duration": 417865, "timestamp": 1218381109579, "id": 23, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1752137272336, "traceId": "50c5f293b46e8fdf"}, {"name": "add-entry", "duration": 497681, "timestamp": 1218381109501, "id": 21, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5Csrava%5CDocuments%5Caugment-projects%5Cbnb-website%5Cfrontend%5Csrc%5Capp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1752137272336, "traceId": "50c5f293b46e8fdf"}, {"name": "build-module-tsx", "duration": 18920, "timestamp": 1218381634630, "id": 27, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\frontend\\src\\components\\sections\\ServicesOverview.tsx", "layer": "rsc"}, "startTime": 1752137272861, "traceId": "50c5f293b46e8fdf"}, {"name": "build-module-tsx", "duration": 25396, "timestamp": 1218381629896, "id": 26, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\frontend\\src\\components\\sections\\AboutSection.tsx", "layer": "rsc"}, "startTime": 1752137272856, "traceId": "50c5f293b46e8fdf"}, {"name": "build-module-tsx", "duration": 21761, "timestamp": 1218381634948, "id": 28, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\frontend\\src\\components\\sections\\HeroSection.tsx", "layer": "rsc"}, "startTime": 1752137272862, "traceId": "50c5f293b46e8fdf"}, {"name": "build-module-tsx", "duration": 19286, "timestamp": 1218381638774, "id": 29, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\frontend\\src\\components\\layout\\Header.tsx", "layer": "rsc"}, "startTime": 1752137272865, "traceId": "50c5f293b46e8fdf"}, {"name": "add-entry", "duration": 550203, "timestamp": 1218381108844, "id": 20, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=C%3A%5CUsers%5Csrava%5CDocuments%5Caugment-projects%5Cbnb-website%5Cfrontend%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1752137272335, "traceId": "50c5f293b46e8fdf"}, {"name": "add-entry", "duration": 551229, "timestamp": 1218381109598, "id": 24, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csrava%5CDocuments%5Caugment-projects%5Cbnb-website%5Cfrontend%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1752137272336, "traceId": "50c5f293b46e8fdf"}, {"name": "build-module-tsx", "duration": 32895, "timestamp": 1218381735679, "id": 41, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\frontend\\src\\components\\sections\\AboutSection.tsx", "layer": "ssr"}, "startTime": 1752137272962, "traceId": "50c5f293b46e8fdf"}, {"name": "build-module-tsx", "duration": 41548, "timestamp": 1218381735967, "id": 42, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\frontend\\src\\components\\sections\\HeroSection.tsx", "layer": "ssr"}, "startTime": 1752137272963, "traceId": "50c5f293b46e8fdf"}, {"name": "build-module-tsx", "duration": 51379, "timestamp": 1218381733299, "id": 40, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\frontend\\src\\components\\layout\\Header.tsx", "layer": "ssr"}, "startTime": 1752137272960, "traceId": "50c5f293b46e8fdf"}, {"name": "build-module-tsx", "duration": 53913, "timestamp": 1218381736155, "id": 43, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\frontend\\src\\components\\sections\\ServicesOverview.tsx", "layer": "ssr"}, "startTime": 1752137272963, "traceId": "50c5f293b46e8fdf"}, {"name": "build-module-tsx", "duration": 15857, "timestamp": 1218382071134, "id": 44, "parentId": 41, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\frontend\\src\\components\\ui\\Button.tsx", "layer": "ssr"}, "startTime": 1752137273298, "traceId": "50c5f293b46e8fdf"}, {"name": "make", "duration": 1015362, "timestamp": 1218381108256, "id": 19, "parentId": 17, "tags": {}, "startTime": 1752137272335, "traceId": "50c5f293b46e8fdf"}, {"name": "get-entries", "duration": 2193, "timestamp": 1218382126431, "id": 46, "parentId": 45, "tags": {}, "startTime": 1752137273353, "traceId": "50c5f293b46e8fdf"}, {"name": "node-file-trace-plugin", "duration": 99375, "timestamp": 1218382132547, "id": 47, "parentId": 45, "tags": {"traceEntryCount": "8"}, "startTime": 1752137273359, "traceId": "50c5f293b46e8fdf"}, {"name": "collect-traced-files", "duration": 481, "timestamp": 1218382231934, "id": 48, "parentId": 45, "tags": {}, "startTime": 1752137273459, "traceId": "50c5f293b46e8fdf"}, {"name": "finish-modules", "duration": 106568, "timestamp": 1218382125853, "id": 45, "parentId": 18, "tags": {}, "startTime": 1752137273352, "traceId": "50c5f293b46e8fdf"}, {"name": "chunk-graph", "duration": 21113, "timestamp": 1218382293831, "id": 50, "parentId": 49, "tags": {}, "startTime": 1752137273520, "traceId": "50c5f293b46e8fdf"}, {"name": "optimize-modules", "duration": 59, "timestamp": 1218382315221, "id": 52, "parentId": 49, "tags": {}, "startTime": 1752137273542, "traceId": "50c5f293b46e8fdf"}, {"name": "optimize-chunks", "duration": 21340, "timestamp": 1218382315491, "id": 53, "parentId": 49, "tags": {}, "startTime": 1752137273542, "traceId": "50c5f293b46e8fdf"}, {"name": "optimize-tree", "duration": 566, "timestamp": 1218382336969, "id": 54, "parentId": 49, "tags": {}, "startTime": 1752137273564, "traceId": "50c5f293b46e8fdf"}, {"name": "optimize-chunk-modules", "duration": 29411, "timestamp": 1218382337669, "id": 55, "parentId": 49, "tags": {}, "startTime": 1752137273564, "traceId": "50c5f293b46e8fdf"}, {"name": "optimize", "duration": 52089, "timestamp": 1218382315106, "id": 51, "parentId": 49, "tags": {}, "startTime": 1752137273542, "traceId": "50c5f293b46e8fdf"}, {"name": "module-hash", "duration": 30760, "timestamp": 1218382392344, "id": 56, "parentId": 49, "tags": {}, "startTime": 1752137273619, "traceId": "50c5f293b46e8fdf"}, {"name": "code-generation", "duration": 38063, "timestamp": 1218382423211, "id": 57, "parentId": 49, "tags": {}, "startTime": 1752137273650, "traceId": "50c5f293b46e8fdf"}, {"name": "hash", "duration": 9679, "timestamp": 1218382467253, "id": 58, "parentId": 49, "tags": {}, "startTime": 1752137273694, "traceId": "50c5f293b46e8fdf"}, {"name": "code-generation-jobs", "duration": 251, "timestamp": 1218382476930, "id": 59, "parentId": 49, "tags": {}, "startTime": 1752137273704, "traceId": "50c5f293b46e8fdf"}, {"name": "module-assets", "duration": 389, "timestamp": 1218382477142, "id": 60, "parentId": 49, "tags": {}, "startTime": 1752137273704, "traceId": "50c5f293b46e8fdf"}, {"name": "create-chunk-assets", "duration": 3926, "timestamp": 1218382477547, "id": 61, "parentId": 49, "tags": {}, "startTime": 1752137273704, "traceId": "50c5f293b46e8fdf"}, {"name": "minify-js", "duration": 9557, "timestamp": 1218382496282, "id": 63, "parentId": 62, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1752137273723, "traceId": "50c5f293b46e8fdf"}, {"name": "minify-js", "duration": 9413, "timestamp": 1218382496440, "id": 64, "parentId": 62, "tags": {"name": "../app/favicon.ico/route.js", "cache": "HIT"}, "startTime": 1752137273723, "traceId": "50c5f293b46e8fdf"}, {"name": "minify-js", "duration": 9403, "timestamp": 1218382496453, "id": 65, "parentId": 62, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1752137273723, "traceId": "50c5f293b46e8fdf"}, {"name": "minify-js", "duration": 9396, "timestamp": 1218382496461, "id": 66, "parentId": 62, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1752137273723, "traceId": "50c5f293b46e8fdf"}, {"name": "minify-js", "duration": 1246, "timestamp": 1218382504612, "id": 68, "parentId": 62, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1752137273731, "traceId": "50c5f293b46e8fdf"}, {"name": "minify-js", "duration": 1197, "timestamp": 1218382504663, "id": 69, "parentId": 62, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1752137273731, "traceId": "50c5f293b46e8fdf"}, {"name": "minify-js", "duration": 1167, "timestamp": 1218382504695, "id": 70, "parentId": 62, "tags": {"name": "683.js", "cache": "HIT"}, "startTime": 1752137273731, "traceId": "50c5f293b46e8fdf"}, {"name": "minify-js", "duration": 1072, "timestamp": 1218382504791, "id": 71, "parentId": 62, "tags": {"name": "382.js", "cache": "HIT"}, "startTime": 1752137273731, "traceId": "50c5f293b46e8fdf"}, {"name": "minify-js", "duration": 975, "timestamp": 1218382504889, "id": 72, "parentId": 62, "tags": {"name": "160.js", "cache": "HIT"}, "startTime": 1752137273731, "traceId": "50c5f293b46e8fdf"}, {"name": "minify-js", "duration": 16382, "timestamp": 1218382505014, "id": 73, "parentId": 62, "tags": {"name": "19.js", "cache": "MISS"}, "startTime": 1752137273732, "traceId": "50c5f293b46e8fdf"}, {"name": "minify-js", "duration": 49451, "timestamp": 1218382496471, "id": 67, "parentId": 62, "tags": {"name": "../app/page.js", "cache": "MISS"}, "startTime": 1752137273723, "traceId": "50c5f293b46e8fdf"}, {"name": "minify-webpack-plugin-optimize", "duration": 59646, "timestamp": 1218382486299, "id": 62, "parentId": 17, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1752137273713, "traceId": "50c5f293b46e8fdf"}, {"name": "css-minimizer-plugin", "duration": 192, "timestamp": 1218382546159, "id": 74, "parentId": 17, "tags": {}, "startTime": 1752137273773, "traceId": "50c5f293b46e8fdf"}, {"name": "create-trace-assets", "duration": 1394, "timestamp": 1218382546607, "id": 75, "parentId": 18, "tags": {}, "startTime": 1752137273773, "traceId": "50c5f293b46e8fdf"}, {"name": "seal", "duration": 299397, "timestamp": 1218382263622, "id": 49, "parentId": 17, "tags": {}, "startTime": 1752137273490, "traceId": "50c5f293b46e8fdf"}, {"name": "webpack-compilation", "duration": 1482531, "timestamp": 1218381093872, "id": 17, "parentId": 14, "tags": {"name": "server"}, "startTime": 1752137272320, "traceId": "50c5f293b46e8fdf"}, {"name": "emit", "duration": 93657, "timestamp": 1218382577038, "id": 76, "parentId": 14, "tags": {}, "startTime": 1752137273804, "traceId": "50c5f293b46e8fdf"}, {"name": "webpack-close", "duration": 363569, "timestamp": 1218382674737, "id": 77, "parentId": 14, "tags": {"name": "server"}, "startTime": 1752137273901, "traceId": "50c5f293b46e8fdf"}, {"name": "webpack-generate-error-stats", "duration": 6667, "timestamp": 1218383038513, "id": 78, "parentId": 77, "tags": {}, "startTime": 1752137274265, "traceId": "50c5f293b46e8fdf"}, {"name": "run-webpack-compiler", "duration": 2664181, "timestamp": 1218380381386, "id": 14, "parentId": 13, "tags": {}, "startTime": 1752137271608, "traceId": "50c5f293b46e8fdf"}, {"name": "format-webpack-messages", "duration": 114, "timestamp": 1218383045579, "id": 79, "parentId": 13, "tags": {}, "startTime": 1752137274272, "traceId": "50c5f293b46e8fdf"}, {"name": "worker-main-server", "duration": 2665260, "timestamp": 1218380380614, "id": 13, "parentId": 1, "tags": {}, "startTime": 1752137271607, "traceId": "50c5f293b46e8fdf"}, {"name": "create-entrypoints", "duration": 25721, "timestamp": 1218384048353, "id": 82, "parentId": 80, "tags": {}, "startTime": 1752137275275, "traceId": "50c5f293b46e8fdf"}, {"name": "generate-webpack-config", "duration": 392170, "timestamp": 1218384074325, "id": 83, "parentId": 81, "tags": {}, "startTime": 1752137275301, "traceId": "50c5f293b46e8fdf"}, {"name": "make", "duration": 1029, "timestamp": 1218384596910, "id": 85, "parentId": 84, "tags": {}, "startTime": 1752137275823, "traceId": "50c5f293b46e8fdf"}, {"name": "chunk-graph", "duration": 711, "timestamp": 1218384601006, "id": 87, "parentId": 86, "tags": {}, "startTime": 1752137275828, "traceId": "50c5f293b46e8fdf"}, {"name": "optimize-modules", "duration": 32, "timestamp": 1218384601834, "id": 89, "parentId": 86, "tags": {}, "startTime": 1752137275828, "traceId": "50c5f293b46e8fdf"}, {"name": "optimize-chunks", "duration": 1226, "timestamp": 1218384601951, "id": 90, "parentId": 86, "tags": {}, "startTime": 1752137275828, "traceId": "50c5f293b46e8fdf"}, {"name": "optimize-tree", "duration": 139, "timestamp": 1218384603253, "id": 91, "parentId": 86, "tags": {}, "startTime": 1752137275830, "traceId": "50c5f293b46e8fdf"}, {"name": "optimize-chunk-modules", "duration": 1100, "timestamp": 1218384604460, "id": 92, "parentId": 86, "tags": {}, "startTime": 1752137275831, "traceId": "50c5f293b46e8fdf"}, {"name": "optimize", "duration": 4286, "timestamp": 1218384601782, "id": 88, "parentId": 86, "tags": {}, "startTime": 1752137275828, "traceId": "50c5f293b46e8fdf"}, {"name": "module-hash", "duration": 137, "timestamp": 1218384607599, "id": 93, "parentId": 86, "tags": {}, "startTime": 1752137275834, "traceId": "50c5f293b46e8fdf"}, {"name": "code-generation", "duration": 297, "timestamp": 1218384607788, "id": 94, "parentId": 86, "tags": {}, "startTime": 1752137275834, "traceId": "50c5f293b46e8fdf"}, {"name": "hash", "duration": 520, "timestamp": 1218384608486, "id": 95, "parentId": 86, "tags": {}, "startTime": 1752137275835, "traceId": "50c5f293b46e8fdf"}, {"name": "code-generation-jobs", "duration": 233, "timestamp": 1218384609004, "id": 96, "parentId": 86, "tags": {}, "startTime": 1752137275836, "traceId": "50c5f293b46e8fdf"}, {"name": "module-assets", "duration": 96, "timestamp": 1218384609202, "id": 97, "parentId": 86, "tags": {}, "startTime": 1752137275836, "traceId": "50c5f293b46e8fdf"}, {"name": "create-chunk-assets", "duration": 247, "timestamp": 1218384609309, "id": 98, "parentId": 86, "tags": {}, "startTime": 1752137275836, "traceId": "50c5f293b46e8fdf"}, {"name": "minify-js", "duration": 248, "timestamp": 1218384626955, "id": 100, "parentId": 99, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1752137275853, "traceId": "50c5f293b46e8fdf"}, {"name": "minify-webpack-plugin-optimize", "duration": 4369, "timestamp": 1218384622844, "id": 99, "parentId": 84, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1752137275849, "traceId": "50c5f293b46e8fdf"}, {"name": "css-minimizer-plugin", "duration": 126, "timestamp": 1218384627308, "id": 101, "parentId": 84, "tags": {}, "startTime": 1752137275854, "traceId": "50c5f293b46e8fdf"}, {"name": "seal", "duration": 30257, "timestamp": 1218384600383, "id": 86, "parentId": 84, "tags": {}, "startTime": 1752137275827, "traceId": "50c5f293b46e8fdf"}, {"name": "webpack-compilation", "duration": 45458, "timestamp": 1218384585515, "id": 84, "parentId": 81, "tags": {"name": "edge-server"}, "startTime": 1752137275812, "traceId": "50c5f293b46e8fdf"}, {"name": "emit", "duration": 8541, "timestamp": 1218384631312, "id": 102, "parentId": 81, "tags": {}, "startTime": 1752137275858, "traceId": "50c5f293b46e8fdf"}, {"name": "webpack-close", "duration": 797, "timestamp": 1218384641560, "id": 103, "parentId": 81, "tags": {"name": "edge-server"}, "startTime": 1752137275868, "traceId": "50c5f293b46e8fdf"}, {"name": "webpack-generate-error-stats", "duration": 2552, "timestamp": 1218384642411, "id": 104, "parentId": 103, "tags": {}, "startTime": 1752137275869, "traceId": "50c5f293b46e8fdf"}, {"name": "run-webpack-compiler", "duration": 596701, "timestamp": 1218384048344, "id": 81, "parentId": 80, "tags": {}, "startTime": 1752137275275, "traceId": "50c5f293b46e8fdf"}, {"name": "format-webpack-messages", "duration": 82, "timestamp": 1218384645050, "id": 105, "parentId": 80, "tags": {}, "startTime": 1752137275872, "traceId": "50c5f293b46e8fdf"}, {"name": "worker-main-edge-server", "duration": 597590, "timestamp": 1218384047730, "id": 80, "parentId": 1, "tags": {}, "startTime": 1752137275274, "traceId": "50c5f293b46e8fdf"}, {"name": "create-entrypoints", "duration": 26629, "timestamp": 1218385480968, "id": 108, "parentId": 106, "tags": {}, "startTime": 1752137276708, "traceId": "50c5f293b46e8fdf"}, {"name": "generate-webpack-config", "duration": 382805, "timestamp": 1218385507843, "id": 109, "parentId": 107, "tags": {}, "startTime": 1752137276735, "traceId": "50c5f293b46e8fdf"}, {"name": "add-entry", "duration": 429899, "timestamp": 1218386028143, "id": 115, "parentId": 111, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1752137277255, "traceId": "50c5f293b46e8fdf"}, {"name": "add-entry", "duration": 575602, "timestamp": 1218386028164, "id": 116, "parentId": 111, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1752137277255, "traceId": "50c5f293b46e8fdf"}, {"name": "add-entry", "duration": 575797, "timestamp": 1218386028119, "id": 114, "parentId": 111, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csrava%5C%5CDocuments%5C%5Caugment-projects%5C%5Cbnb-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csrava%5C%5CDocuments%5C%5Caugment-projects%5C%5Cbnb-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csrava%5C%5CDocuments%5C%5Caugment-projects%5C%5Cbnb-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csrava%5C%5CDocuments%5C%5Caugment-projects%5C%5Cbnb-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csrava%5C%5CDocuments%5C%5Caugment-projects%5C%5Cbnb-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csrava%5C%5CDocuments%5C%5Caugment-projects%5C%5Cbnb-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csrava%5C%5CDocuments%5C%5Caugment-projects%5C%5Cbnb-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csrava%5C%5CDocuments%5C%5Caugment-projects%5C%5Cbnb-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752137277255, "traceId": "50c5f293b46e8fdf"}, {"name": "add-entry", "duration": 575823, "timestamp": 1218386028177, "id": 117, "parentId": 111, "tags": {"request": "C:\\Users\\<USER>\\Documents\\augment-projects\\bnb-website\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1752137277255, "traceId": "50c5f293b46e8fdf"}, {"name": "add-entry", "duration": 575823, "timestamp": 1218386028191, "id": 118, "parentId": 111, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1752137277255, "traceId": "50c5f293b46e8fdf"}, {"name": "add-entry", "duration": 1021092, "timestamp": 1218386027602, "id": 112, "parentId": 111, "tags": {"request": "./../node_modules/next/dist/client/next.js"}, "startTime": 1752137277254, "traceId": "50c5f293b46e8fdf"}, {"name": "add-entry", "duration": 1020644, "timestamp": 1218386028085, "id": 113, "parentId": 111, "tags": {"request": "./../node_modules/next/dist/client/app-next.js"}, "startTime": 1752137277255, "traceId": "50c5f293b46e8fdf"}, {"name": "postcss-process", "duration": 227242, "timestamp": 1218387002260, "id": 124, "parentId": 123, "tags": {}, "startTime": 1752137278229, "traceId": "50c5f293b46e8fdf"}]