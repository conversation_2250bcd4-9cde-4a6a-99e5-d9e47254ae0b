'use strict';

var format = require('./format');

/*
 * function cascade(formats)
 * Returns a function that invokes the `._format` function in-order
 * for the specified set of `formats`. In this manner we say that Formats
 * are "pipe-like", but not a pure pumpify implementation. Since there is no back
 * pressure we can remove all of the "readable" plumbing in Node streams.
 */
function cascade(formats) {
  if (!formats.every(isValidFormat)) {
    return;
  }
  return function (info) {
    var obj = info;
    for (var i = 0; i < formats.length; i++) {
      obj = formats[i].transform(obj, formats[i].options);
      if (!obj) {
        return false;
      }
    }
    return obj;
  };
}

/*
 * function isValidFormat(format)
 * If the format does not define a `transform` function throw an error
 * with more detailed usage.
 */
function isValidFormat(fmt) {
  if (typeof fmt.transform !== 'function') {
    throw new Error(['No transform function found on format. Did you create a format instance?', 'const myFormat = format(formatFn);', 'const instance = myFormat();'].join('\n'));
  }
  return true;
}

/*
 * function combine (info)
 * Returns a new instance of the combine Format which combines the specified
 * formats into a new format. This is similar to a pipe-chain in transform streams.
 * We choose to combine the prototypes this way because there is no back pressure in
 * an in-memory transform chain.
 */
module.exports = function () {
  for (var _len = arguments.length, formats = new Array(_len), _key = 0; _key < _len; _key++) {
    formats[_key] = arguments[_key];
  }
  var combinedFormat = format(cascade(formats));
  var instance = combinedFormat();
  instance.Format = combinedFormat.Format;
  return instance;
};

//
// Export the cascade method for use in cli and other
// combined formats that should not be assumed to be
// singletons.
//
module.exports.cascade = cascade;