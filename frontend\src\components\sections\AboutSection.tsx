"use client";

import React from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>ircle,
  ArrowRight,
  Target,
  Users,
  Lightbulb,
  Award,
} from "lucide-react";
import { Button } from "@/components/ui/Button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import {
  fadeInUp,
  fadeInLeft,
  fadeInRight,
  staggerContainer,
} from "@/lib/utils";

const features = [
  {
    icon: Target,
    title: "Mission-Driven",
    description:
      "Empowering businesses through innovative technology solutions that drive real results.",
  },
  {
    icon: Users,
    title: "Expert Team",
    description:
      "Skilled professionals with years of experience in cutting-edge technologies.",
  },
  {
    icon: Lightbulb,
    title: "Innovation First",
    description:
      "We stay ahead of technology trends to deliver future-ready solutions.",
  },
  {
    icon: Award,
    title: "Quality Assured",
    description:
      "Rigorous testing and quality assurance processes ensure excellence.",
  },
];

const achievements = [
  "End-to-end digital transformation",
  "Scalable and secure solutions",
  "Agile development methodology",
  "Post-launch support and maintenance",
  "Transparent communication",
  "Competitive pricing",
];

export default function AboutSection() {
  return (
    <section className="py-20 lg:py-32 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20 items-center">
          {/* Left Content */}
          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
          >
            <motion.h2
              variants={fadeInLeft}
              className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6"
            >
              About <span className="text-primary">BytesNBinary</span>
            </motion.h2>

            <motion.p
              variants={fadeInLeft}
              className="text-lg text-muted-foreground mb-8 leading-relaxed"
            >
              BytesNBinary Innovations is a forward-thinking IT startup
              dedicated to transforming businesses through comprehensive digital
              solutions. We combine technical expertise with creative innovation
              to deliver exceptional results.
            </motion.p>

            <motion.div variants={fadeInLeft} className="space-y-4 mb-8">
              {achievements.map((achievement, index) => (
                <motion.div
                  key={achievement}
                  variants={fadeInLeft}
                  custom={index}
                  className="flex items-center space-x-3"
                >
                  <CheckCircle className="w-5 h-5 text-secondary flex-shrink-0" />
                  <span className="text-foreground">{achievement}</span>
                </motion.div>
              ))}
            </motion.div>

            <motion.div variants={fadeInLeft}>
              <Link href="/about" className="group">
                <Button size="lg">
                  Learn More About Us
                  <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </motion.div>
          </motion.div>

          {/* Right Content - Feature Cards */}
          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            className="grid grid-cols-1 sm:grid-cols-2 gap-6"
          >
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                variants={fadeInRight}
                custom={index}
              >
                <Card className="h-full text-center group hover:shadow-lg transition-all duration-300">
                  <CardHeader>
                    <motion.div
                      className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl flex items-center justify-center group-hover:from-primary/20 group-hover:to-secondary/20 transition-all duration-300"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                    >
                      <feature.icon className="w-6 h-6 text-primary" />
                    </motion.div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground text-sm leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Mission Statement */}
        <motion.div
          variants={fadeInUp}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="mt-20 text-center"
        >
          <div className="max-w-4xl mx-auto">
            <h3 className="text-2xl lg:text-3xl font-bold text-foreground mb-6">
              Our Mission
            </h3>
            <p className="text-lg lg:text-xl text-muted-foreground leading-relaxed">
              "To empower businesses of all sizes with innovative technology
              solutions that drive growth, enhance efficiency, and create
              lasting value in an increasingly digital world."
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
