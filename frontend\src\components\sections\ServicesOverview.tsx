"use client";

import React from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import {
  ArrowR<PERSON>,
  Code,
  Smartphone,
  Cloud,
  Shield,
  Palette,
  Brain,
  TrendingUp,
  Layers,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { SERVICES_DATA } from "@/lib/constants";
import { fadeInUp, staggerContainer, scaleIn } from "@/lib/utils";

const iconMap = {
  Code,
  Smartphone,
  Cloud,
  Shield,
  Palette,
  Brain,
  TrendingUp,
  Layers,
};

export default function ServicesOverview() {
  return (
    <section className="py-20 lg:py-32 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={staggerContainer}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          className="text-center mb-16"
        >
          <motion.h2
            variants={fadeInUp}
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6"
          >
            Our <span className="text-primary">Services</span>
          </motion.h2>
          <motion.p
            variants={fadeInUp}
            className="text-lg lg:text-xl text-muted-foreground max-w-3xl mx-auto"
          >
            We offer comprehensive digital solutions to help your business
            thrive in the modern world. From web development to AI solutions,
            we've got you covered.
          </motion.p>
        </motion.div>

        <motion.div
          variants={staggerContainer}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-50px" }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12"
        >
          {SERVICES_DATA.map((service, index) => {
            const IconComponent = iconMap[service.icon as keyof typeof iconMap];

            return (
              <motion.div key={service.id} variants={scaleIn} custom={index}>
                <Card className="h-full group cursor-pointer border-2 hover:border-primary/50 transition-all duration-300">
                  <CardHeader className="text-center">
                    <motion.div
                      className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl flex items-center justify-center group-hover:from-primary/20 group-hover:to-secondary/20 transition-all duration-300"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                    >
                      <IconComponent className="w-8 h-8 text-primary" />
                    </motion.div>
                    <CardTitle className="text-lg group-hover:text-primary transition-colors">
                      {service.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-center line-clamp-3">
                      {service.description}
                    </CardDescription>
                    <div className="mt-4 flex flex-wrap gap-2 justify-center">
                      {service.features.slice(0, 2).map((feature) => (
                        <span
                          key={feature}
                          className="text-xs px-2 py-1 bg-muted rounded-full text-muted-foreground"
                        >
                          {feature}
                        </span>
                      ))}
                      {service.features.length > 2 && (
                        <span className="text-xs px-2 py-1 bg-muted rounded-full text-muted-foreground">
                          +{service.features.length - 2} more
                        </span>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </motion.div>

        <motion.div
          variants={fadeInUp}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="text-center"
        >
          <Link href="/services" className="group">
            <Button size="lg">
              View All Services
              <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
