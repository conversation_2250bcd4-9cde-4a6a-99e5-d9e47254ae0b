{"name": "fecha", "version": "4.2.3", "description": "Date formatting and parsing", "main": "lib/fecha.umd.js", "module": "lib/fecha.js", "scripts": {"test-only": "ts-node test.js", "test": "prettier --check *.js src/*.ts && eslint --ext .ts src && npm run build && nyc --cache --reporter=text ts-node test.js", "build": "NODE_ENV=production rollup -c --sourcemap && tsc", "format": "prettier --write *.js src/*.ts"}, "repository": {"type": "git", "url": "https://<EMAIL>/taylorhakes/fecha.git"}, "keywords": ["date", "parse", "moment", "format", "fecha", "formatting"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/taylorhakes/fecha/issues"}, "homepage": "https://github.com/taylorhakes/fecha", "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.1", "@typescript-eslint/eslint-plugin": "^2.14.0", "@typescript-eslint/parser": "^2.14.0", "eslint": "^7.23.0", "eslint-config-prettier": "^8.1.0", "nyc": "^15.0.0", "painless": "^0.9.7", "prettier": "1.19.1", "rollup": "^0.59.0", "rollup-plugin-sourcemaps": "^0.5.0", "rollup-plugin-typescript": "^1.0.1", "rollup-plugin-uglify": "^3.0.0", "source-map-support": "^0.5.16", "ts-node": "^8.5.4", "tslib": "^1.10.0", "typescript": "^3.7.4"}, "files": ["lib", "dist", "src"], "types": "lib/fecha.d.ts"}