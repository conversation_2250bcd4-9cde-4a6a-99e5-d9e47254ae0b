{"version": 3, "file": "isTypeReadonly.js", "sourceRoot": "", "sources": ["../src/isTypeReadonly.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,oDAAuD;AAEvD,sDAAwC;AACxC,+CAAiC;AAEjC,mDAA0D;AAE1D,iEAGgC;AAgBnB,QAAA,yBAAyB,GAAG;IACvC,IAAI,EAAE,QAAQ;IACd,oBAAoB,EAAE,KAAK;IAC3B,UAAU,EAAE;QACV,sBAAsB,EAAE;YACtB,IAAI,EAAE,SAAS;SAChB;QACD,KAAK,EAAE;YACL,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,iDAA0B;SAClC;KACF;CACoB,CAAC;AAEX,QAAA,2BAA2B,GAAwB;IAC9D,sBAAsB,EAAE,KAAK;IAC7B,KAAK,EAAE,EAAE;CACV,CAAC;AAEF,SAAS,SAAS,CAAC,IAAa;IAC9B,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC9D,CAAC;AAED,SAAS,0BAA0B,CACjC,OAAmB,EACnB,IAAa,EACb,OAA4B,EAC5B,SAAuB;IAEvB,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;IACzC,SAAS,kBAAkB,CAAC,SAA2B;QACrD,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAE1D,uCAAuC;QACvC,4CAA4C;QAC5C,oDAAoD;QACpD,wBAAwB,CAAC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxD,qCAA6B;QAC/B,CAAC;QAED,+CAA+C;QAC/C,IACE,aAAa,CAAC,IAAI,CAChB,OAAO,CAAC,EAAE,CACR,sBAAsB,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC;wCACxC,CACvB,EACD,CAAC;YACD,oCAA4B;QAC9B,CAAC;QACD,qCAA6B;IAC/B,CAAC;IAED,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,mBAAW,CAAC,UAAU,CACnC,IAAI,CAAC,SAAS,EAAE,EAChB,mBAAW,CAAC,iBAAiB,CAAC,YAAY,CAAC,QAAQ,EAAE,YAAY,CAAC,CACnE,CAAC;QACF,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;QAC5C,wEAAwE;QACxE,IAAI,WAAW,KAAK,OAAO,EAAE,CAAC;YAC5B,oCAA4B;QAC9B,CAAC;QAED,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC1B,oCAA4B;QAC9B,CAAC;QAED,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,wCAAgC;AAClC,CAAC;AAED,SAAS,oBAAoB,CAC3B,OAAmB,EACnB,IAAa,EACb,OAA4B,EAC5B,SAAuB;IAEvB,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;IACzC,SAAS,mBAAmB,CAAC,IAAkB;QAC7C,MAAM,SAAS,GAAG,OAAO,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACzD,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;gBAC1B,oCAA4B;YAC9B,CAAC;YAED,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,IAAI,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7D,qCAA6B;YAC/B,CAAC;YAED,OAAO,sBAAsB,CAC3B,OAAO,EACP,SAAS,CAAC,IAAI,EACd,OAAO,EACP,SAAS,CACV,CAAC;QACJ,CAAC;QAED,wCAAgC;IAClC,CAAC;IAED,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;IACxC,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;QACtB,+CAA+C;QAC/C,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE,CAAC;YAClC,IAAI,OAAO,CAAC,sBAAsB,EAAE,CAAC;gBACnC,IACE,QAAQ,CAAC,gBAAgB,KAAK,SAAS;oBACvC,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC;oBACpC,OAAO,CAAC,eAAe,CACrB,QAAQ,CAAC,gBAAgB,CAAC,MAAM,EAChC,EAAE,CAAC,WAAW,CAAC,MAAM,CACtB,EACD,CAAC;oBACD,SAAS;gBACX,CAAC;gBAED,MAAM,YAAY,GAAG,QAAQ,CAAC,eAAe,EAAE,CAAC;gBAChD,MAAM,eAAe,GACnB,YAAY,KAAK,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC;oBACnD,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;oBACvC,CAAC,CAAC,SAAS,CAAC;gBAChB,IACE,eAAe,KAAK,SAAS;oBAC7B,SAAS,CAAC,eAAe,CAAC;oBAC1B,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,EACtE,CAAC;oBACD,SAAS;gBACX,CAAC;YACH,CAAC;YAED,IACE,OAAO,CAAC,wBAAwB,CAC9B,IAAI,EACJ,QAAQ,CAAC,cAAc,EAAE,EACzB,OAAO,CACR,EACD,CAAC;gBACD,SAAS;YACX,CAAC;YAED,MAAM,IAAI,GAAG,EAAE,CAAC,oBAAoB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAChE,IAAI,IAAI,IAAI,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzC,SAAS;YACX,CAAC;YAED,oCAA4B;QAC9B,CAAC;QAED,+BAA+B;QAC/B,uDAAuD;QAEvD,wEAAwE;QACxE,yEAAyE;QACzE,gDAAgD;QAChD,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE,CAAC;YAClC,MAAM,YAAY,GAAG,mBAAW,CAAC,UAAU,CACzC,IAAA,uCAAuB,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,EAChD,mBAAW,CAAC,iBAAiB,CAAC,YAAY,CACxC,aAAa,QAAQ,CAAC,IAAI,GAAG,EAC7B,MAAM,CACP,CACF,CAAC;YAEF,0BAA0B;YAC1B,gHAAgH;YAChH,IAAI,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;gBAChC,SAAS;YACX,CAAC;YAED,IACE,sBAAsB,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC;4CAC7C,EACpB,CAAC;gBACD,oCAA4B;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM,wBAAwB,GAAG,mBAAmB,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC1E,IAAI,wBAAwB,iCAAyB,EAAE,CAAC;QACtD,OAAO,wBAAwB,CAAC;IAClC,CAAC;IAED,MAAM,wBAAwB,GAAG,mBAAmB,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC1E,IAAI,wBAAwB,iCAAyB,EAAE,CAAC;QACtD,OAAO,wBAAwB,CAAC;IAClC,CAAC;IAED,qCAA6B;AAC/B,CAAC;AAED,qGAAqG;AACrG,SAAS,sBAAsB,CAC7B,OAAmB,EACnB,IAAa,EACb,OAA4B,EAC5B,SAAuB;IAEvB,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;IACzC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAEpB,IACE,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAC9B,IAAA,2CAAoB,EAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAC/C,EACD,CAAC;QACD,qCAA6B;IAC/B,CAAC;IAED,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9B,0CAA0C;QAC1C,MAAM,MAAM,GAAG,OAAO;aACnB,cAAc,CAAC,IAAI,CAAC;aACpB,KAAK,CACJ,CAAC,CAAC,EAAE,CACF,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,sBAAsB,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,SAAS,CAAC;6CAC/B,CAC1B,CAAC;QACJ,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,+BAAuB,CAAC,6BAAqB,CAAC;QAC3E,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,IAAI,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;QACrC,mGAAmG;QACnG,IACE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EACtE,CAAC;YACD,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CACvC,CAAC,CAAC,EAAE,CACF,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;gBAChB,sBAAsB,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,SAAS,CAAC;iDAC/B,CAC1B,CAAC;YACF,OAAO,gBAAgB,CAAC,CAAC,+BAAuB,CAAC,6BAAqB,CAAC;QACzE,CAAC;QAED,eAAe;QACf,MAAM,gBAAgB,GAAG,oBAAoB,CAC3C,OAAO,EACP,IAAI,EACJ,OAAO,EACP,SAAS,CACV,CAAC;QACF,IAAI,gBAAgB,qCAA6B,EAAE,CAAC;YAClD,OAAO,gBAAgB,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;QACpC,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;aAC/D,GAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC;aAChC,KAAK,CACJ,CAAC,CAAC,EAAE,CACF,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,sBAAsB,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,SAAS,CAAC;6CAC/B,CAC1B,CAAC;QAEJ,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,+BAAuB,CAAC,6BAAqB,CAAC;QAC3E,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,uDAAuD;IACvD,sCAAsC;IACtC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;QAChC,qCAA6B;IAC/B,CAAC;IAED,mCAAmC;IACnC,IACE,IAAI,CAAC,iBAAiB,EAAE,CAAC,MAAM,GAAG,CAAC;QACnC,IAAI,CAAC,aAAa,EAAE,CAAC,MAAM,KAAK,CAAC,EACjC,CAAC;QACD,qCAA6B;IAC/B,CAAC;IAED,MAAM,eAAe,GAAG,0BAA0B,CAChD,OAAO,EACP,IAAI,EACJ,OAAO,EACP,SAAS,CACV,CAAC;IACF,IAAI,eAAe,qCAA6B,EAAE,CAAC;QACjD,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,MAAM,gBAAgB,GAAG,oBAAoB,CAC3C,OAAO,EACP,IAAI,EACJ,OAAO,EACP,SAAS,CACV,CAAC;IACF,0BAA0B,CAAC,IACzB,gBAAgB,qCAA6B,EAC7C,CAAC;QACD,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;AACpC,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CACrB,OAAmB,EACnB,IAAa,EACb,UAA+B,mCAA2B;IAE1D,OAAO,CACL,sBAAsB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,CAAC;qCACpC,CACtB,CAAC;AACJ,CAAC;AAEQ,wCAAc"}