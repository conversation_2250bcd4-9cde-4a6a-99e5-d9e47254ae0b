{"version": 3, "file": "parser-options.d.ts", "sourceRoot": "", "sources": ["../src/parser-options.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAE1C,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,OAAO,CAAC;AAEjC,KAAK,UAAU,GAAG,CAAC,QAAQ,GAAG,mBAAmB,GAAG,YAAY,CAAC,EAAE,GAAG,OAAO,CAAC;AAC9E,KAAK,oBAAoB,GAAG,MAAM,GAAG,UAAU,CAAC;AAEhD,KAAK,WAAW,GACZ,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,CAAC;AAET,KAAK,iBAAiB,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC7C,KAAK,UAAU,GAAG,iBAAiB,GAAG,UAAU,CAAC;AAEjD,KAAK,gBAAgB,GAAG,KAAK,GAAG,MAAM,GAAG,WAAW,CAAC;AAGrD,UAAU,aAAa;IACrB,YAAY,CAAC,EAAE;QACb,YAAY,CAAC,EAAE,OAAO,CAAC;QACvB,GAAG,CAAC,EAAE,OAAO,CAAC;QACd,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC;KACxB,CAAC;IACF,WAAW,CAAC,EAAE,WAAW,GAAG,QAAQ,CAAC;IAGrC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC1B,eAAe,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAChC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;IAGZ,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAGhC,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,UAAU,CAAC,EAAE,UAAU,CAAC;IACxB,2CAA2C,CAAC,EAAE,OAAO,CAAC;IACtD,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC,8BAA8B,CAAC,EAAE,OAAO,CAAC;IACzC,gDAAgD,CAAC,EAAE,OAAO,CAAC;IAC3D,mBAAmB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC/B,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IACpC,GAAG,CAAC,EAAE,OAAO,CAAC;IACd,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAC1B,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC;IAC7C,uBAAuB,CAAC,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC;IAC9C,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,UAAU,CAAC,EAAE,UAAU,CAAC;IACxB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,kCAAkC,CAAC,EAAE,OAAO,CAAC;IAC7C,aAAa,CAAC,EAAE;QACd,IAAI,CAAC,EAAE,oBAAoB,CAAC;KAC7B,CAAC;IAEF,CAAC,oBAAoB,EAAE,MAAM,GAAG,OAAO,CAAC;CACzC;AAED,OAAO,EACL,oBAAoB,EACpB,UAAU,EACV,WAAW,EACX,gBAAgB,EAChB,aAAa,EACb,UAAU,GACX,CAAC"}