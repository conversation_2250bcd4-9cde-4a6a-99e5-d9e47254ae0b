{"/Users/<USER>/triple-beam/index.js": {"path": "/Users/<USER>/triple-beam/index.js", "statementMap": {"0": {"start": {"line": 11, "column": 0}, "end": {"line": 13, "column": 3}}, "1": {"start": {"line": 23, "column": 0}, "end": {"line": 25, "column": 3}}, "2": {"start": {"line": 34, "column": 0}, "end": {"line": 36, "column": 3}}, "3": {"start": {"line": 44, "column": 0}, "end": {"line": 46, "column": 3}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "cc95d94182fb79b1be57a24c1394db64cdb2ae43", "contentHash": "fc4e5a28fd95e03cae6b5495203bb1875d4000296fe4308cb90943317c6cd273"}, "/Users/<USER>/triple-beam/config/index.js": {"path": "/Users/<USER>/triple-beam/config/index.js", "statementMap": {"0": {"start": {"line": 14, "column": 0}, "end": {"line": 16, "column": 3}}, "1": {"start": {"line": 22, "column": 0}, "end": {"line": 24, "column": 3}}, "2": {"start": {"line": 30, "column": 0}, "end": {"line": 32, "column": 3}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "153b0bdac58673ccf1f21070117bb797c37a2456", "contentHash": "162d744ed325a346a9b0913721d2f61535053e170a78dc205aef5ae6aef820de"}, "/Users/<USER>/triple-beam/config/cli.js": {"path": "/Users/<USER>/triple-beam/config/cli.js", "statementMap": {"0": {"start": {"line": 14, "column": 0}, "end": {"line": 25, "column": 2}}, "1": {"start": {"line": 31, "column": 0}, "end": {"line": 42, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "4843c10f2957d007fb3125cc3a04d3b940a44851", "contentHash": "3c526501a0b8bf50d0bbc28e6d4a1baf4c1798586fe1154292a1d9ba3a77efaf"}, "/Users/<USER>/triple-beam/config/npm.js": {"path": "/Users/<USER>/triple-beam/config/npm.js", "statementMap": {"0": {"start": {"line": 14, "column": 0}, "end": {"line": 22, "column": 2}}, "1": {"start": {"line": 28, "column": 0}, "end": {"line": 36, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "56fb16df83c6d6275045feafabbe26c61f098c4b", "contentHash": "07a4e369cf657d332a918f7ca96bcc1b831720e571cc2902aec1feed0c598920"}, "/Users/<USER>/triple-beam/config/syslog.js": {"path": "/Users/<USER>/triple-beam/config/syslog.js", "statementMap": {"0": {"start": {"line": 14, "column": 0}, "end": {"line": 23, "column": 2}}, "1": {"start": {"line": 29, "column": 0}, "end": {"line": 38, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "4a8f05f93061e8c418dd384c10b82586a7f6e950", "contentHash": "6177cc185f053def4bdb52172b771267aa2ee7ff9e11208e7f88ec323f1b058a"}}