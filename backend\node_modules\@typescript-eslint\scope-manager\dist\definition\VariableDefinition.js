"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VariableDefinition = void 0;
const DefinitionBase_1 = require("./DefinitionBase");
const DefinitionType_1 = require("./DefinitionType");
class VariableDefinition extends DefinitionBase_1.DefinitionBase {
    constructor(name, node, decl) {
        super(DefinitionType_1.DefinitionType.Variable, name, node, decl);
        this.isTypeDefinition = false;
        this.isVariableDefinition = true;
    }
}
exports.VariableDefinition = VariableDefinition;
//# sourceMappingURL=VariableDefinition.js.map